import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

ApplicationWindow {
    id: window
    width: 1200
    height: 800
    visible: true
    title: "CANoeLite Test"
    
    ColumnLayout {
        anchors.fill: parent
        spacing: 0
        
        // Quick Access Toolbar
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 32
            color: "#F2F2F2"
            border.color: "#CCCCCC"
            border.width: 1
            
            Text {
                anchors.centerIn: parent
                text: "Quick Access Toolbar"
                font.pixelSize: 12
            }
        }
        
        // Ribbon
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 120
            color: "#FFFFFF"
            border.color: "#CCCCCC"
            border.width: 1
            
            Text {
                anchors.centerIn: parent
                text: "Ribbon Interface"
                font.pixelSize: 14
            }
        }
        
        // Desktop Tabs
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 32
            color: "#E0E0E0"
            border.color: "#CCCCCC"
            border.width: 1
            
            Text {
                anchors.centerIn: parent
                text: "Desktop Tabs"
                font.pixelSize: 12
            }
        }
        
        // Main Area
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "#F8F8F8"
            
            Text {
                anchors.centerIn: parent
                text: "Main Dock Area"
                font.pixelSize: 16
            }
        }
        
        // Status Bar
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 24
            color: "#E0E0E0"
            border.color: "#CCCCCC"
            border.width: 1
            
            Text {
                anchors.centerIn: parent
                text: "Status Bar"
                font.pixelSize: 11
            }
        }
    }
}

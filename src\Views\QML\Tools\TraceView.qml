/**
 * @file TraceView.qml
 * @brief TraceView tool window for CANoeLite
 * <AUTHOR> Team
 * @date 2025
 */

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import CANoeLite 1.0
import ".."

Rectangle {
    id: traceView
    
    property string toolName: "TraceView"
    property string toolTitle: "Trace View"
    property bool isFloating: false
    property bool isMaximized: false
    
    color: Theme.colors.surface
    border.color: Theme.colors.dockBorder
    border.width: 1
    
    ColumnLayout {
        anchors.fill: parent
        spacing: 0
        
        // Title Bar
        Rectangle {
            id: titleBar
            Layout.fillWidth: true
            Layout.preferredHeight: Theme.sizing.dockTitleBarHeight
            color: Theme.colors.dockTitleBar
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: Theme.spacing.xs
                
                Text {
                    text: traceView.toolTitle
                    color: Theme.colors.textOnPrimary
                    font.family: Theme.typography.fontFamily
                    font.pixelSize: Theme.typography.fontSizeSmall
                    font.weight: Theme.typography.fontWeightMedium
                    Layout.fillWidth: true
                }
                
                // Window controls
                Row {
                    spacing: Theme.spacing.xs
                    
                    ToolButton {
                        width: 16
                        height: 16
                        text: "🗗"
                        font.pixelSize: 8
                        onClicked: {
                            traceView.isMaximized = !traceView.isMaximized
                            DockViewModel.setToolMaximized(traceView.toolName, traceView.isMaximized)
                        }
                    }
                    
                    ToolButton {
                        width: 16
                        height: 16
                        text: "⚓"
                        font.pixelSize: 8
                        onClicked: {
                            traceView.isFloating = !traceView.isFloating
                            DockViewModel.setToolFloating(traceView.toolName, traceView.isFloating)
                        }
                    }
                    
                    ToolButton {
                        width: 16
                        height: 16
                        text: "✕"
                        font.pixelSize: 8
                        onClicked: DockViewModel.closeTool(traceView.toolName)
                    }
                }
            }
            
            // Make title bar draggable
            MouseArea {
                anchors.fill: parent
                drag.target: traceView.isFloating ? traceView : null
                onDoubleClicked: {
                    traceView.isMaximized = !traceView.isMaximized
                    DockViewModel.setToolMaximized(traceView.toolName, traceView.isMaximized)
                }
            }
        }
        
        // Toolbar
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 32
            color: Theme.colors.surfaceLight
            border.color: Theme.colors.borderLight
            border.width: 1
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: Theme.spacing.sm
                
                Button {
                    text: "Start"
                    Layout.preferredWidth: 60
                    Layout.preferredHeight: 24
                    onClicked: traceModel.startTrace()
                }
                
                Button {
                    text: "Stop"
                    Layout.preferredWidth: 60
                    Layout.preferredHeight: 24
                    onClicked: traceModel.stopTrace()
                }
                
                Button {
                    text: "Clear"
                    Layout.preferredWidth: 60
                    Layout.preferredHeight: 24
                    onClicked: traceModel.clearTrace()
                }
                
                Rectangle {
                    width: 1
                    height: parent.height * 0.6
                    color: Theme.colors.border
                }
                
                CheckBox {
                    text: "Auto Scroll"
                    checked: true
                }
                
                Item {
                    Layout.fillWidth: true
                }
                
                Text {
                    text: "Messages: " + traceModel.messageCount
                    color: Theme.colors.textSecondary
                    font.family: Theme.typography.fontFamily
                    font.pixelSize: Theme.typography.fontSizeSmall
                }
            }
        }
        
        // Content Area
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: Theme.colors.surfaceLight
            border.color: Theme.colors.borderLight
            border.width: 1
            
            ScrollView {
                anchors.fill: parent
                anchors.margins: 1
                
                ListView {
                    id: traceListView
                    model: traceModel
                    
                    delegate: Rectangle {
                        width: traceListView.width
                        height: 20
                        color: index % 2 === 0 ? Theme.colors.surfaceLight : Theme.colors.surface
                        
                        RowLayout {
                            anchors.fill: parent
                            anchors.margins: Theme.spacing.xs
                            spacing: Theme.spacing.sm
                            
                            Text {
                                text: model.timestamp || "00:00:00.000"
                                color: Theme.colors.textSecondary
                                font.family: Theme.typography.monoFontFamily
                                font.pixelSize: Theme.typography.fontSizeSmall
                                Layout.preferredWidth: 80
                            }
                            
                            Text {
                                text: model.id || "0x123"
                                color: Theme.colors.textPrimary
                                font.family: Theme.typography.monoFontFamily
                                font.pixelSize: Theme.typography.fontSizeSmall
                                Layout.preferredWidth: 60
                            }
                            
                            Text {
                                text: model.direction || "Rx"
                                color: model.direction === "Tx" ? Theme.colors.primary : Theme.colors.info
                                font.family: Theme.typography.fontFamily
                                font.pixelSize: Theme.typography.fontSizeSmall
                                Layout.preferredWidth: 30
                            }
                            
                            Text {
                                text: model.data || "01 02 03 04 05 06 07 08"
                                color: Theme.colors.textPrimary
                                font.family: Theme.typography.monoFontFamily
                                font.pixelSize: Theme.typography.fontSizeSmall
                                Layout.fillWidth: true
                            }
                        }
                    }
                    
                    // Header
                    header: Rectangle {
                        width: traceListView.width
                        height: 24
                        color: Theme.colors.surfaceDark
                        border.color: Theme.colors.border
                        border.width: 1
                        
                        RowLayout {
                            anchors.fill: parent
                            anchors.margins: Theme.spacing.xs
                            spacing: Theme.spacing.sm
                            
                            Text {
                                text: "Time"
                                color: Theme.colors.textPrimary
                                font.family: Theme.typography.fontFamily
                                font.pixelSize: Theme.typography.fontSizeSmall
                                font.weight: Theme.typography.fontWeightBold
                                Layout.preferredWidth: 80
                            }
                            
                            Text {
                                text: "ID"
                                color: Theme.colors.textPrimary
                                font.family: Theme.typography.fontFamily
                                font.pixelSize: Theme.typography.fontSizeSmall
                                font.weight: Theme.typography.fontWeightBold
                                Layout.preferredWidth: 60
                            }
                            
                            Text {
                                text: "Dir"
                                color: Theme.colors.textPrimary
                                font.family: Theme.typography.fontFamily
                                font.pixelSize: Theme.typography.fontSizeSmall
                                font.weight: Theme.typography.fontWeightBold
                                Layout.preferredWidth: 30
                            }
                            
                            Text {
                                text: "Data"
                                color: Theme.colors.textPrimary
                                font.family: Theme.typography.fontFamily
                                font.pixelSize: Theme.typography.fontSizeSmall
                                font.weight: Theme.typography.fontWeightBold
                                Layout.fillWidth: true
                            }
                        }
                    }
                }
            }
        }
        
        // Status Bar
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 20
            color: Theme.colors.statusBarBackground
            border.color: Theme.colors.border
            border.width: 1
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: Theme.spacing.xs
                
                Text {
                    text: traceModel.isRunning ? "Recording..." : "Stopped"
                    color: traceModel.isRunning ? Theme.colors.success : Theme.colors.textSecondary
                    font.family: Theme.typography.fontFamily
                    font.pixelSize: Theme.typography.fontSizeXSmall
                }
                
                Item {
                    Layout.fillWidth: true
                }
                
                Text {
                    text: "Rate: " + traceModel.messageRate + " msg/s"
                    color: Theme.colors.textSecondary
                    font.family: Theme.typography.fontFamily
                    font.pixelSize: Theme.typography.fontSizeXSmall
                }
            }
        }
    }
    
    // Sample trace model
    QtObject {
        id: traceModel
        
        property bool isRunning: false
        property int messageCount: 0
        property int messageRate: 0
        
        function startTrace() {
            isRunning = true
            console.log("TraceView: Started tracing")
        }
        
        function stopTrace() {
            isRunning = false
            console.log("TraceView: Stopped tracing")
        }
        
        function clearTrace() {
            messageCount = 0
            console.log("TraceView: Cleared trace")
        }
    }
}

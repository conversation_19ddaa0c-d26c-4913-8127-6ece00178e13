cmake_minimum_required(VERSION 3.21)

project(CANoeLite
    VERSION 1.0.0
    DESCRIPTION "A simplified Vector CANoe-style desktop application"
    LANGUAGES CXX
)

# Set C++ standard
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set default build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "Debug" CACHE STRING "Choose the type of build." FORCE)
    set_property(CACHE CMAKE_BUILD_TYPE PROPERTY STRINGS "Debug" "Release" "MinSizeRel" "RelWithDebInfo")
endif()

# Find required Qt6 components
find_package(Qt6 REQUIRED COMPONENTS
    Core
    Quick
    QuickControls2
    Widgets
)

# Enable Qt6 features
qt_standard_project_setup()

# Set output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Add subdirectories
add_subdirectory(src)

# Install configuration
set(CMAKE_INSTALL_PREFIX "${CMAKE_BINARY_DIR}/install" CACHE PATH "Installation directory")

# Create install target for release packages
install(TARGETS CANoeLite
    BUNDLE DESTINATION .
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

# Install Qt6 dependencies for Windows
if(WIN32)
    find_program(WINDEPLOYQT_EXECUTABLE windeployqt HINTS ${Qt6_DIR}/../../../bin)
    if(WINDEPLOYQT_EXECUTABLE)
        install(CODE "
            execute_process(
                COMMAND ${WINDEPLOYQT_EXECUTABLE} --qmldir ${CMAKE_SOURCE_DIR}/src/Views/QML \$ENV{DESTDIR}\${CMAKE_INSTALL_PREFIX}/bin/CANoeLite.exe
                WORKING_DIRECTORY \${CMAKE_INSTALL_PREFIX}
            )
        ")
    endif()
endif()

# Enable testing
enable_testing()
add_subdirectory(tests)

/**
 * @file Graphics.qml
 * @brief Graphics tool window for CANoeLite
 * <AUTHOR> Team
 * @date 2025
 */

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import CANoeLite 1.0
import ".."

Rectangle {
    id: graphics
    
    property string toolName: "Graphics"
    property string toolTitle: "Graphics"
    property bool isFloating: false
    property bool isMaximized: false
    
    // Theme colors
    readonly property color primaryColor: "#7A1E2A"
    readonly property color surfaceColor: "#F2F2F2"
    readonly property color surfaceLight: "#FFFFFF"
    readonly property color borderColor: "#CCCCCC"
    readonly property color textPrimary: "#212121"
    readonly property color textSecondary: "#757575"
    readonly property color textOnPrimary: "#FFFFFF"
    
    color: surfaceColor
    border.color: borderColor
    border.width: 1
    
    ColumnLayout {
        anchors.fill: parent
        spacing: 0
        
        // Title Bar
        Rectangle {
            id: titleBar
            Layout.fillWidth: true
            Layout.preferredHeight: 24
            color: primaryColor
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 2
                
                Text {
                    text: graphics.toolTitle
                    color: textOnPrimary
                    font.pixelSize: 11
                    font.weight: Font.Medium
                    Layout.fillWidth: true
                }
                
                // Window controls
                Row {
                    spacing: 2
                    
                    ToolButton {
                        width: 16
                        height: 16
                        text: "🗗"
                        font.pixelSize: 8
                        background: Rectangle {
                            color: parent.hovered ? "#A52A3A" : "transparent"
                            radius: 2
                        }
                        onClicked: {
                            graphics.isMaximized = !graphics.isMaximized
                            console.log("Toggle maximize:", graphics.toolName)
                        }
                    }
                    
                    ToolButton {
                        width: 16
                        height: 16
                        text: "⚓"
                        font.pixelSize: 8
                        background: Rectangle {
                            color: parent.hovered ? "#A52A3A" : "transparent"
                            radius: 2
                        }
                        onClicked: {
                            graphics.isFloating = !graphics.isFloating
                            console.log("Toggle floating:", graphics.toolName)
                        }
                    }
                    
                    ToolButton {
                        width: 16
                        height: 16
                        text: "✕"
                        font.pixelSize: 8
                        background: Rectangle {
                            color: parent.hovered ? "#A52A3A" : "transparent"
                            radius: 2
                        }
                        onClicked: console.log("Close tool:", graphics.toolName)
                    }
                }
            }
        }
        
        // Toolbar
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 32
            color: surfaceLight
            border.color: borderColor
            border.width: 1
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 4
                
                Button {
                    text: "New Chart"
                    Layout.preferredWidth: 80
                    Layout.preferredHeight: 24
                    background: Rectangle {
                        color: parent.pressed ? primaryColor : (parent.hovered ? "#A52A3A" : surfaceColor)
                        border.color: borderColor
                        border.width: 1
                        radius: 3
                    }
                    onClicked: console.log("New Chart")
                }
                
                Button {
                    text: "Save Chart"
                    Layout.preferredWidth: 80
                    Layout.preferredHeight: 24
                    background: Rectangle {
                        color: parent.pressed ? primaryColor : (parent.hovered ? "#A52A3A" : surfaceColor)
                        border.color: borderColor
                        border.width: 1
                        radius: 3
                    }
                    onClicked: console.log("Save Chart")
                }
                
                Rectangle {
                    width: 1
                    height: parent.height * 0.6
                    color: borderColor
                }
                
                ComboBox {
                    model: ["Line Chart", "Bar Chart", "Scatter Plot", "Histogram"]
                    currentIndex: 0
                    Layout.preferredWidth: 120
                    Layout.preferredHeight: 24
                    background: Rectangle {
                        color: surfaceLight
                        border.color: borderColor
                        border.width: 1
                        radius: 2
                    }
                }
                
                Item {
                    Layout.fillWidth: true
                }
                
                CheckBox {
                    text: "Auto Scale"
                    checked: true
                    font.pixelSize: 10
                }
            }
        }
        
        // Content Area
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: surfaceLight
            border.color: borderColor
            border.width: 1
            
            // Sample chart area
            Rectangle {
                anchors.fill: parent
                anchors.margins: 20
                color: "white"
                border.color: borderColor
                border.width: 1
                
                // Chart grid
                Canvas {
                    id: chartCanvas
                    anchors.fill: parent
                    
                    onPaint: {
                        var ctx = getContext("2d")
                        ctx.clearRect(0, 0, width, height)
                        
                        // Draw grid
                        ctx.strokeStyle = "#E0E0E0"
                        ctx.lineWidth = 1
                        
                        // Vertical lines
                        for (var x = 0; x <= width; x += width / 10) {
                            ctx.beginPath()
                            ctx.moveTo(x, 0)
                            ctx.lineTo(x, height)
                            ctx.stroke()
                        }
                        
                        // Horizontal lines
                        for (var y = 0; y <= height; y += height / 8) {
                            ctx.beginPath()
                            ctx.moveTo(0, y)
                            ctx.lineTo(width, y)
                            ctx.stroke()
                        }
                        
                        // Draw sample data line
                        ctx.strokeStyle = "#7A1E2A"
                        ctx.lineWidth = 2
                        ctx.beginPath()
                        
                        var points = []
                        for (var i = 0; i <= 100; i++) {
                            var x = (i / 100) * width
                            var y = height/2 + Math.sin(i * 0.1) * height/4 + Math.random() * 20 - 10
                            points.push({x: x, y: y})
                        }
                        
                        ctx.moveTo(points[0].x, points[0].y)
                        for (var j = 1; j < points.length; j++) {
                            ctx.lineTo(points[j].x, points[j].y)
                        }
                        ctx.stroke()
                    }
                    
                    Component.onCompleted: requestPaint()
                }
                
                // Chart labels
                Text {
                    anchors.bottom: parent.bottom
                    anchors.horizontalCenter: parent.horizontalCenter
                    anchors.bottomMargin: -15
                    text: "Time (s)"
                    color: textSecondary
                    font.pixelSize: 10
                }
                
                Text {
                    anchors.left: parent.left
                    anchors.verticalCenter: parent.verticalCenter
                    anchors.leftMargin: -15
                    text: "Value"
                    color: textSecondary
                    font.pixelSize: 10
                    rotation: -90
                }
                
                // Legend
                Rectangle {
                    anchors.top: parent.top
                    anchors.right: parent.right
                    anchors.margins: 10
                    width: 120
                    height: 60
                    color: surfaceLight
                    border.color: borderColor
                    border.width: 1
                    radius: 3
                    
                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 8
                        
                        Text {
                            text: "Legend"
                            font.weight: Font.Bold
                            color: textPrimary
                            font.pixelSize: 10
                        }
                        
                        Row {
                            spacing: 4
                            Rectangle {
                                width: 12
                                height: 2
                                color: primaryColor
                            }
                            Text {
                                text: "Engine Speed"
                                color: textPrimary
                                font.pixelSize: 9
                            }
                        }
                        
                        Row {
                            spacing: 4
                            Rectangle {
                                width: 12
                                height: 2
                                color: "#2196F3"
                            }
                            Text {
                                text: "Vehicle Speed"
                                color: textPrimary
                                font.pixelSize: 9
                            }
                        }
                    }
                }
            }
        }
        
        // Status Bar
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 20
            color: "#E0E0E0"
            border.color: borderColor
            border.width: 1
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 4
                
                Text {
                    text: "Chart: Line Chart"
                    color: textSecondary
                    font.pixelSize: 10
                }
                
                Item {
                    Layout.fillWidth: true
                }
                
                Text {
                    text: "Data points: 1,247"
                    color: textSecondary
                    font.pixelSize: 10
                }
                
                Text {
                    text: "Range: 0-60s"
                    color: textSecondary
                    font.pixelSize: 10
                }
            }
        }
    }
}

/**
 * @file DockManager.h
 * @brief Dock window management service for CANoeLite
 * <AUTHOR> Team
 * @date 2025
 */

#pragma once

#include <QObject>
#include <QStringList>
#include <QVariantMap>
#include <QSettings>

namespace Services {

/**
 * @brief Manages dock-like tool windows and their layouts
 */
class DockManager : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QStringList openTools READ openTools NOTIFY openToolsChanged)

public:
    /**
     * @brief Tool window information structure
     */
    struct ToolInfo {
        QString name;           ///< Tool name
        QString title;          ///< Display title
        QString qmlSource;      ///< QML source file
        QVariantMap geometry;   ///< Window geometry
        bool isFloating;        ///< Floating state
        bool isVisible;         ///< Visibility state
        bool isMaximized;       ///< Maximized state
    };

    /**
     * @brief Constructor
     * @param parent Parent object
     */
    explicit DockManager(QObject* parent = nullptr);
    
    /**
     * @brief Destructor
     */
    ~DockManager() override;
    
    /**
     * @brief Get list of open tool names
     * @return List of open tool names
     */
    QStringList openTools() const { return m_openTools; }

public slots:
    /**
     * @brief Open a tool window
     * @param toolName Name of the tool to open
     * @return True if tool was opened successfully
     */
    bool openTool(const QString& toolName);
    
    /**
     * @brief Close a tool window
     * @param toolName Name of the tool to close
     */
    void closeTool(const QString& toolName);
    
    /**
     * @brief Toggle tool window visibility
     * @param toolName Name of the tool to toggle
     */
    void toggleTool(const QString& toolName);
    
    /**
     * @brief Set tool window geometry
     * @param toolName Tool name
     * @param geometry Geometry map (x, y, width, height)
     */
    void setToolGeometry(const QString& toolName, const QVariantMap& geometry);
    
    /**
     * @brief Set tool window floating state
     * @param toolName Tool name
     * @param floating Floating state
     */
    void setToolFloating(const QString& toolName, bool floating);
    
    /**
     * @brief Set tool window maximized state
     * @param toolName Tool name
     * @param maximized Maximized state
     */
    void setToolMaximized(const QString& toolName, bool maximized);
    
    /**
     * @brief Save current layout to settings
     * @param layoutName Layout name
     */
    void saveLayout(const QString& layoutName = "default");
    
    /**
     * @brief Load layout from settings
     * @param layoutName Layout name
     */
    void loadLayout(const QString& layoutName = "default");
    
    /**
     * @brief Get available layout names
     * @return List of available layout names
     */
    QStringList getAvailableLayouts() const;
    
    /**
     * @brief Delete a saved layout
     * @param layoutName Layout name to delete
     */
    void deleteLayout(const QString& layoutName);

signals:
    /**
     * @brief Emitted when the list of open tools changes
     */
    void openToolsChanged();
    
    /**
     * @brief Emitted when a tool should be opened
     * @param toolName Tool name
     * @param toolInfo Tool information
     */
    void toolOpened(const QString& toolName, const QVariantMap& toolInfo);
    
    /**
     * @brief Emitted when a tool should be closed
     * @param toolName Tool name
     */
    void toolClosed(const QString& toolName);
    
    /**
     * @brief Emitted when tool geometry changes
     * @param toolName Tool name
     * @param geometry New geometry
     */
    void toolGeometryChanged(const QString& toolName, const QVariantMap& geometry);

private:
    /**
     * @brief Initialize default tool configurations
     */
    void initializeDefaultTools();
    
    /**
     * @brief Get tool information by name
     * @param toolName Tool name
     * @return Tool information or nullptr if not found
     */
    ToolInfo* getToolInfo(const QString& toolName);
    
    /**
     * @brief Load layout settings
     */
    void loadLayoutSettings();
    
    /**
     * @brief Save layout settings
     */
    void saveLayoutSettings();

private:
    QStringList m_openTools;                    ///< List of open tool names
    QMap<QString, ToolInfo> m_toolInfos;       ///< Tool information map
    QSettings* m_settings;                      ///< Settings instance
    QString m_currentLayout;                    ///< Current layout name
};

} // namespace Services

/**
 * @file DockViewModel.cpp
 * @brief Implementation of DockViewModel
 * <AUTHOR> Team
 * @date 2025
 */

#include "DockViewModel.h"
#include "../Services/DockManager.h"

namespace ViewModels {

DockViewModel::DockViewModel(Services::DockManager* dockManager, QObject* parent)
    : QObject(parent)
    , m_dockManager(dockManager)
{
    // Connect to dock manager signals
    connect(m_dockManager, &Services::DockManager::openToolsChanged,
            this, &DockViewModel::openToolsChanged);
    
    connect(m_dockManager, &Services::DockManager::toolOpened,
            this, &DockViewModel::toolOpened);
    
    connect(m_dockManager, &Services::DockManager::toolClosed,
            this, &DockViewModel::toolClosed);
}

QStringList DockViewModel::openTools() const
{
    return m_dockManager->openTools();
}

void DockViewModel::openTool(const QString& toolName)
{
    m_dockManager->openTool(toolName);
}

void DockViewModel::closeTool(const QString& toolName)
{
    m_dockManager->closeTool(toolName);
}

void DockViewModel::toggleTool(const QString& toolName)
{
    m_dockManager->toggleTool(toolName);
}

void DockViewModel::setToolGeometry(const QString& toolName, const QVariantMap& geometry)
{
    m_dockManager->setToolGeometry(toolName, geometry);
}

void DockViewModel::setToolFloating(const QString& toolName, bool floating)
{
    m_dockManager->setToolFloating(toolName, floating);
}

void DockViewModel::setToolMaximized(const QString& toolName, bool maximized)
{
    m_dockManager->setToolMaximized(toolName, maximized);
}

void DockViewModel::saveLayout(const QString& layoutName)
{
    m_dockManager->saveLayout(layoutName);
}

void DockViewModel::loadLayout(const QString& layoutName)
{
    m_dockManager->loadLayout(layoutName);
}

QStringList DockViewModel::getAvailableLayouts() const
{
    return m_dockManager->getAvailableLayouts();
}

} // namespace ViewModels

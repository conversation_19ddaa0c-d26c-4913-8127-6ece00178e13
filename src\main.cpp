/**
 * @file main.cpp
 * @brief Main entry point for CANoeLite application
 * <AUTHOR> Team
 * @date 2025
 */

#include <QGuiApplication>
#include <QQmlApplicationEngine>
#include <QQmlContext>
#include <QQuickStyle>
#include <QDir>
#include <QStandardPaths>

#include "Core/Application.h"
#include "ViewModels/MainViewModel.h"
#include "ViewModels/RibbonViewModel.h"
#include "ViewModels/DockViewModel.h"
#include "Services/DockManager.h"
#include "Services/ThemeManager.h"

int main(int argc, char *argv[])
{
    // Create application instance
    QGuiApplication app(argc, argv);

    // Set application properties
    app.setApplicationName("CANoeLite");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("CANoeLite Team");
    app.setOrganizationDomain("canoe-lite.com");

    // Set Fusion style for modern appearance
    QQuickStyle::setStyle("Fusion");
    
    // Initialize core application
    Core::Application coreApp;
    if (!coreApp.initialize()) {
        return -1;
    }

    // Create services
    auto* dockManager = new Services::DockManager(&app);
    auto* themeManager = new Services::ThemeManager(&app);
    
    // Create view models
    auto* mainViewModel = new ViewModels::MainViewModel(&app);
    auto* ribbonViewModel = new ViewModels::RibbonViewModel(&app);
    auto* dockViewModel = new ViewModels::DockViewModel(dockManager, &app);

    // Connect ribbon to dock system
    QObject::connect(ribbonViewModel, &ViewModels::RibbonViewModel::openToolWindow,
                     dockManager, &Services::DockManager::openTool);
    
    // Setup QML engine
    QQmlApplicationEngine engine;
    
    // Register C++ types with QML
    qmlRegisterSingletonInstance("CANoeLite", 1, 0, "MainViewModel", mainViewModel);
    qmlRegisterSingletonInstance("CANoeLite", 1, 0, "RibbonViewModel", ribbonViewModel);
    qmlRegisterSingletonInstance("CANoeLite", 1, 0, "DockViewModel", dockViewModel);
    qmlRegisterSingletonInstance("CANoeLite", 1, 0, "ThemeManager", themeManager);
    
    // Set context properties
    engine.rootContext()->setContextProperty("dockManager", dockManager);
    
    // Load main QML file
    const QUrl url(QStringLiteral("qrc:/qt/qml/CANoeLite/Views/QML/main.qml"));
    QObject::connect(&engine, &QQmlApplicationEngine::objectCreated,
                     &app, [url](QObject *obj, const QUrl &objUrl) {
        if (!obj && url == objUrl)
            QCoreApplication::exit(-1);
    }, Qt::QueuedConnection);
    
    engine.load(url);
    
    return app.exec();
}

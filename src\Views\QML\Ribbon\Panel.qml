/**
 * @file Panel.qml
 * @brief Ribbon panel component for grouping related buttons
 * <AUTHOR> Team
 * @date 2025
 */

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import ".."

Rectangle {
    id: panel
    
    property string title: ""
    property alias content: contentArea.children
    default property alias children: contentArea.children
    
    implicitWidth: Math.max(Theme.sizing.ribbonPanelMinWidth, contentArea.implicitWidth + 2 * Theme.spacing.md)
    implicitHeight: parent.height
    
    color: Theme.colors.ribbonPanelBackground
    border.color: Theme.colors.borderLight
    border.width: 1
    radius: Theme.radius.small
    
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: Theme.spacing.sm
        spacing: Theme.spacing.xs
        
        // Content area
        Item {
            id: contentArea
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.minimumHeight: 60
        }
        
        // Panel title
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 16
            color: "transparent"
            
            Text {
                anchors.centerIn: parent
                text: panel.title
                color: Theme.colors.textSecondary
                font.family: Theme.typography.fontFamily
                font.pixelSize: Theme.typography.fontSizeXSmall
                font.weight: Theme.typography.fontWeightMedium
                elide: Text.ElideRight
                horizontalAlignment: Text.AlignHCenter
            }
        }
    }
}

/**
 * @file Application.cpp
 * @brief Implementation of Core application class
 * <AUTHOR> Team
 * @date 2025
 */

#include "Application.h"
#include <QCoreApplication>
#include <QStandardPaths>
#include <QDir>
#include <QDebug>

namespace Core {

Application::Application(QObject* parent)
    : QObject(parent)
    , m_settings(nullptr)
    , m_initialized(false)
{
    // Connect to application quit signal
    connect(QCoreApplication::instance(), &QCoreApplication::aboutToQuit,
            this, &Application::onAboutToQuit);
}

Application::~Application()
{
    shutdown();
}

bool Application::initialize()
{
    if (m_initialized) {
        return true;
    }
    
    qDebug() << "Initializing CANoeLite application...";
    
    // Setup directories
    setupDirectories();
    
    // Load settings
    loadSettings();
    
    m_initialized = true;
    qDebug() << "CANoeLite application initialized successfully";
    
    return true;
}

void Application::shutdown()
{
    if (!m_initialized) {
        return;
    }
    
    qDebug() << "Shutting down CANoeLite application...";
    
    // Save settings
    if (m_settings) {
        m_settings->sync();
        delete m_settings;
        m_settings = nullptr;
    }
    
    emit aboutToQuit();
    
    m_initialized = false;
    qDebug() << "CANoeLite application shutdown complete";
}

void Application::onAboutToQuit()
{
    shutdown();
}

void Application::setupDirectories()
{
    // Get application data directory
    m_dataDirectory = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    
    // Create directory if it doesn't exist
    QDir dataDir(m_dataDirectory);
    if (!dataDir.exists()) {
        if (!dataDir.mkpath(".")) {
            qWarning() << "Failed to create application data directory:" << m_dataDirectory;
        }
    }
    
    qDebug() << "Application data directory:" << m_dataDirectory;
}

void Application::loadSettings()
{
    // Create settings instance
    m_settings = new QSettings(this);
    
    // Set default values if not present
    if (!m_settings->contains("window/geometry")) {
        m_settings->setValue("window/geometry", QByteArray());
    }
    
    if (!m_settings->contains("window/state")) {
        m_settings->setValue("window/state", QByteArray());
    }
    
    if (!m_settings->contains("ribbon/collapsed")) {
        m_settings->setValue("ribbon/collapsed", false);
    }
    
    qDebug() << "Settings loaded from:" << m_settings->fileName();
}

} // namespace Core

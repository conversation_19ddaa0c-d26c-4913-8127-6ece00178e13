/**
 * @file main.qml
 * @brief Main window for CANoeLite application
 * <AUTHOR> Team
 * @date 2025
 */

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Window 2.15
import CANoeLite 1.0

ApplicationWindow {
    id: mainWindow

    title: "CANoeLite - Vector CANoe Style Desktop Application"
    width: 1200
    height: 800
    minimumWidth: 800
    minimumHeight: 600
    visible: true

    // Main layout
    ColumnLayout {
        anchors.fill: parent
        spacing: 0

        // Quick Access Toolbar
        Rectangle {
            id: quickAccessToolbar
            Layout.fillWidth: true
            Layout.preferredHeight: 32
            color: "#F2F2F2"
            border.color: "#CCCCCC"
            border.width: 1

            RowLayout {
                anchors.fill: parent
                anchors.margins: 4
                spacing: 4

                // Group 1: Window controls
                Row {
                    spacing: 2

                    ToolButton {
                        width: 24
                        height: 24
                        text: "🗗"
                        ToolTip.text: "Restore"
                        onClicked: mainWindow.showNormal()
                    }

                    ToolButton {
                        width: 24
                        height: 24
                        text: "🗕"
                        ToolTip.text: "Minimize"
                        onClicked: mainWindow.showMinimized()
                    }

                    ToolButton {
                        width: 24
                        height: 24
                        text: "🗖"
                        ToolTip.text: "Maximize"
                        onClicked: mainWindow.showMaximized()
                    }

                    ToolButton {
                        width: 24
                        height: 24
                        text: "🗙"
                        ToolTip.text: "Close"
                        onClicked: Qt.quit()
                    }
                }

                // Separator
                Rectangle {
                    width: 1
                    height: parent.height * 0.6
                    color: "#CCCCCC"
                }

                // Group 2: Start/Stop controls
                Row {
                    spacing: 2

                    ToolButton {
                        width: 24
                        height: 24
                        text: "▶"
                        ToolTip.text: "Start"
                        onClicked: console.log("Start clicked")
                    }

                    ToolButton {
                        width: 24
                        height: 24
                        text: "⏹"
                        ToolTip.text: "Stop"
                        onClicked: console.log("Stop clicked")
                    }
                }

                // Separator
                Rectangle {
                    width: 1
                    height: parent.height * 0.6
                    color: "#CCCCCC"
                }

                // Group 3: Configuration controls
                Row {
                    spacing: 2

                    ToolButton {
                        width: 24
                        height: 24
                        text: "📁"
                        ToolTip.text: "Load Configuration"
                        onClicked: console.log("Load clicked")
                    }

                    ToolButton {
                        width: 24
                        height: 24
                        text: "💾"
                        ToolTip.text: "Save Configuration"
                        onClicked: console.log("Save clicked")
                    }

                    ToolButton {
                        width: 24
                        height: 24
                        text: "📄"
                        ToolTip.text: "Save As Configuration"
                        onClicked: console.log("Save As clicked")
                    }
                }

                // Separator
                Rectangle {
                    width: 1
                    height: parent.height * 0.6
                    color: "#CCCCCC"
                }

                // Group 4: Settings
                ToolButton {
                    width: 24
                    height: 24
                    text: "⚙"
                    ToolTip.text: "Settings"
                    onClicked: console.log("Settings clicked")
                }

                // Separator
                Rectangle {
                    width: 1
                    height: parent.height * 0.6
                    color: "#CCCCCC"
                }

                // Group 5: Customize
                ToolButton {
                    width: 24
                    height: 24
                    text: "🔧"
                    ToolTip.text: "Customize Quick Access"
                    onClicked: customizeMenu.open()

                    Menu {
                        id: customizeMenu

                        MenuItem {
                            text: "Customize Quick Access Toolbar"
                            onTriggered: console.log("Customize QAT")
                        }

                        MenuItem {
                            text: "Customize Ribbon"
                            onTriggered: console.log("Customize Ribbon")
                        }
                    }
                }

                // Spacer
                Item {
                    Layout.fillWidth: true
                }
            }
        }

        // Ribbon
        Rectangle {
            id: ribbon
            Layout.fillWidth: true
            Layout.preferredHeight: 120
            color: "#F2F2F2"
            border.color: "#CCCCCC"
            border.width: 1

            ColumnLayout {
                anchors.fill: parent
                spacing: 0

                // Tab Bar
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 24
                    color: "#E0E0E0"
                    border.color: "#CCCCCC"
                    border.width: 1

                    TabBar {
                        id: tabBar
                        anchors.fill: parent

                        TabButton {
                            text: "File"
                            font.pixelSize: 11
                        }

                        TabButton {
                            text: "Home"
                            font.pixelSize: 11
                        }

                        TabButton {
                            text: "Analysis"
                            font.pixelSize: 11
                        }

                        TabButton {
                            text: "Simulation"
                            font.pixelSize: 11
                        }

                        TabButton {
                            text: "Test"
                            font.pixelSize: 11
                        }
                    }
                }

                // Ribbon Content
                StackLayout {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    currentIndex: tabBar.currentIndex

                    // File Tab
                    Rectangle {
                        color: "#FFFFFF"

                        Row {
                            anchors.left: parent.left
                            anchors.top: parent.top
                            anchors.margins: 8
                            spacing: 8

                            Column {
                                spacing: 4

                                Text {
                                    text: "Configuration"
                                    font.pixelSize: 10
                                    color: "#666666"
                                }

                                Row {
                                    spacing: 4

                                    Button {
                                        text: "New"
                                        width: 60
                                        height: 32
                                    }

                                    Button {
                                        text: "Open"
                                        width: 60
                                        height: 32
                                    }

                                    Button {
                                        text: "Save"
                                        width: 60
                                        height: 32
                                    }
                                }
                            }
                        }
                    }

                    // Home Tab
                    Rectangle {
                        color: "#FFFFFF"

                        Row {
                            anchors.left: parent.left
                            anchors.top: parent.top
                            anchors.margins: 8
                            spacing: 16

                            Column {
                                spacing: 4

                                Text {
                                    text: "Simulation"
                                    font.pixelSize: 10
                                    color: "#666666"
                                }

                                Row {
                                    spacing: 4

                                    Button {
                                        text: "Start"
                                        width: 60
                                        height: 32
                                        onClicked: console.log("Start simulation")
                                    }

                                    Button {
                                        text: "Stop"
                                        width: 60
                                        height: 32
                                        onClicked: console.log("Stop simulation")
                                    }
                                }
                            }

                            Column {
                                spacing: 4

                                Text {
                                    text: "View"
                                    font.pixelSize: 10
                                    color: "#666666"
                                }

                                Row {
                                    spacing: 4

                                    Button {
                                        text: "Zoom In"
                                        width: 60
                                        height: 32
                                    }

                                    Button {
                                        text: "Zoom Out"
                                        width: 60
                                        height: 32
                                    }
                                }
                            }
                        }
                    }

                    // Analysis Tab
                    Rectangle {
                        color: "#FFFFFF"

                        Row {
                            anchors.left: parent.left
                            anchors.top: parent.top
                            anchors.margins: 8
                            spacing: 16

                            Column {
                                spacing: 4

                                Text {
                                    text: "Trace"
                                    font.pixelSize: 10
                                    color: "#666666"
                                }

                                Row {
                                    spacing: 4

                                    Button {
                                        text: "Trace View"
                                        width: 80
                                        height: 32
                                        onClicked: {
                                            console.log("Opening TraceView")
                                            // This will be connected to the dock system
                                        }
                                    }

                                    Button {
                                        text: "Statistics"
                                        width: 80
                                        height: 32
                                        onClicked: {
                                            console.log("Opening Statistics")
                                            // This will be connected to the dock system
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // Simulation Tab
                    Rectangle {
                        color: "#FFFFFF"
                        Text {
                            anchors.centerIn: parent
                            text: "Simulation Controls"
                            color: "#666666"
                        }
                    }

                    // Test Tab
                    Rectangle {
                        color: "#FFFFFF"
                        Text {
                            anchors.centerIn: parent
                            text: "Test Controls"
                            color: "#666666"
                        }
                    }
                }
            }
        }

        // Desktop Tabs
        Rectangle {
            id: desktopTabs
            Layout.fillWidth: true
            Layout.preferredHeight: 32
            color: "#E0E0E0"
            border.color: "#CCCCCC"
            border.width: 1

            TabBar {
                id: desktopTabBar
                anchors.fill: parent

                TabButton {
                    text: "Desktop 1"
                    font.pixelSize: 11

                    MouseArea {
                        anchors.fill: parent
                        acceptedButtons: Qt.RightButton
                        onClicked: tabContextMenu.popup()
                    }
                }

                Menu {
                    id: tabContextMenu

                    MenuItem {
                        text: "New Tab"
                        onTriggered: console.log("New tab")
                    }

                    MenuItem {
                        text: "Close Tab"
                        onTriggered: console.log("Close tab")
                    }

                    MenuItem {
                        text: "Rename Tab"
                        onTriggered: console.log("Rename tab")
                    }
                }
            }
        }

        // Main Content Area (Dock Area)
        Rectangle {
            id: dockArea
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "#F8F8F8"

            Text {
                anchors.centerIn: parent
                text: "Dock Area - Tool windows will appear here\nClick ribbon buttons to open tools"
                color: "#757575"
                font.pixelSize: 14
                horizontalAlignment: Text.AlignHCenter
            }
        }

        // Status Bar
        Rectangle {
            id: statusBar
            Layout.fillWidth: true
            Layout.preferredHeight: 24
            color: "#E0E0E0"
            border.color: "#CCCCCC"
            border.width: 1

            RowLayout {
                anchors.fill: parent
                anchors.margins: 4

                // Left: App state
                Text {
                    text: "Ready"
                    color: "#757575"
                    font.pixelSize: 11
                }

                // Spacer
                Item {
                    Layout.fillWidth: true
                }

                // Center: Progress
                ProgressBar {
                    Layout.preferredWidth: 200
                    value: 0.0
                    visible: false
                }

                // Spacer
                Item {
                    Layout.fillWidth: true
                }

                // Right: CAN bitrate and clock
                Row {
                    spacing: 12

                    Text {
                        text: "CAN: 500 kbit/s"
                        color: "#757575"
                        font.pixelSize: 11
                    }

                    Text {
                        id: clockText
                        color: "#757575"
                        font.pixelSize: 11

                        Timer {
                            interval: 1000
                            running: true
                            repeat: true
                            onTriggered: {
                                clockText.text = Qt.formatDateTime(new Date(), "hh:mm:ss")
                            }
                        }

                        Component.onCompleted: {
                            text = Qt.formatDateTime(new Date(), "hh:mm:ss")
                        }
                    }
                }
            }
        }
    }
}

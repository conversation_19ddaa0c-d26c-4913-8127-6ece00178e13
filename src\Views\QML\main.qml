/**
 * @file main.qml
 * @brief Main window for CANoeLite application
 * <AUTHOR> Team
 * @date 2025
 */

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Window 2.15
import CANoeLite 1.0

ApplicationWindow {
    id: mainWindow

    title: "CANoeLite - Vector CANoe Style Desktop Application"
    width: 1200
    height: 800
    minimumWidth: 800
    minimumHeight: 600
    visible: true

    // Theme colors
    readonly property color primaryColor: "#7A1E2A"        // Bordeaux red
    readonly property color primaryLight: "#A52A3A"        // Lighter bordeaux
    readonly property color surfaceColor: "#F2F2F2"        // Light gray
    readonly property color surfaceLight: "#FFFFFF"        // White
    readonly property color surfaceDark: "#E0E0E0"         // Darker gray
    readonly property color backgroundColor: "#F8F8F8"     // Very light gray
    readonly property color borderColor: "#CCCCCC"         // Border gray
    readonly property color textPrimary: "#212121"         // Dark text
    readonly property color textSecondary: "#757575"       // Medium text
    readonly property color textOnPrimary: "#FFFFFF"       // White text on primary

    // Main layout
    ColumnLayout {
        anchors.fill: parent
        spacing: 0

        // Quick Access Toolbar
        Rectangle {
            id: quickAccessToolbar
            Layout.fillWidth: true
            Layout.preferredHeight: 32
            color: surfaceColor
            border.color: borderColor
            border.width: 1

            RowLayout {
                anchors.fill: parent
                anchors.margins: 4
                spacing: 4

                // Group 1: Window controls dropdown
                ToolButton {
                    width: 24
                    height: 24
                    text: "🗔"
                    ToolTip.text: "Window Controls"
                    onClicked: windowControlsMenu.open()

                    Menu {
                        id: windowControlsMenu

                        MenuItem {
                            text: "Restore"
                            onTriggered: mainWindow.showNormal()
                        }

                        MenuItem {
                            text: "Minimize"
                            onTriggered: mainWindow.showMinimized()
                        }

                        MenuItem {
                            text: "Maximize"
                            onTriggered: mainWindow.showMaximized()
                        }

                        MenuSeparator {}

                        MenuItem {
                            text: "Close Application"
                            onTriggered: Qt.quit()
                        }
                    }
                }

                // Separator
                Rectangle {
                    width: 1
                    height: parent.height * 0.6
                    color: borderColor
                }

                // Group 2: Start/Stop controls
                Row {
                    spacing: 2

                    ToolButton {
                        width: 24
                        height: 24
                        text: "▶"
                        ToolTip.text: "Start"
                        background: Rectangle {
                            color: parent.hovered ? primaryLight : "transparent"
                            radius: 2
                        }
                        onClicked: console.log("Start clicked")
                    }

                    ToolButton {
                        width: 24
                        height: 24
                        text: "⏹"
                        ToolTip.text: "Stop"
                        background: Rectangle {
                            color: parent.hovered ? primaryLight : "transparent"
                            radius: 2
                        }
                        onClicked: console.log("Stop clicked")
                    }
                }

                // Separator
                Rectangle {
                    width: 1
                    height: parent.height * 0.6
                    color: borderColor
                }

                // Group 3: Configuration controls
                Row {
                    spacing: 2

                    ToolButton {
                        width: 24
                        height: 24
                        text: "📁"
                        ToolTip.text: "Load Configuration"
                        background: Rectangle {
                            color: parent.hovered ? primaryLight : "transparent"
                            radius: 2
                        }
                        onClicked: console.log("Load clicked")
                    }

                    ToolButton {
                        width: 24
                        height: 24
                        text: "💾"
                        ToolTip.text: "Save Configuration"
                        background: Rectangle {
                            color: parent.hovered ? primaryLight : "transparent"
                            radius: 2
                        }
                        onClicked: console.log("Save clicked")
                    }

                    ToolButton {
                        width: 24
                        height: 24
                        text: "📄"
                        ToolTip.text: "Save As Configuration"
                        background: Rectangle {
                            color: parent.hovered ? primaryLight : "transparent"
                            radius: 2
                        }
                        onClicked: console.log("Save As clicked")
                    }
                }

                // Separator
                Rectangle {
                    width: 1
                    height: parent.height * 0.6
                    color: borderColor
                }

                // Group 4: Settings
                ToolButton {
                    width: 24
                    height: 24
                    text: "⚙"
                    ToolTip.text: "Configuration Settings"
                    background: Rectangle {
                        color: parent.hovered ? primaryLight : "transparent"
                        radius: 2
                    }
                    onClicked: configSettingsDialog.open()
                }

                // Separator
                Rectangle {
                    width: 1
                    height: parent.height * 0.6
                    color: borderColor
                }

                // Group 5: Customize
                ToolButton {
                    width: 24
                    height: 24
                    text: "🔧"
                    ToolTip.text: "Customize Ribbon Settings"
                    background: Rectangle {
                        color: parent.hovered ? primaryLight : "transparent"
                        radius: 2
                    }
                    onClicked: customizeRibbonDialog.open()
                }

                // Spacer
                Item {
                    Layout.fillWidth: true
                }
            }
        }

        // Ribbon
        Rectangle {
            id: ribbon
            Layout.fillWidth: true
            Layout.preferredHeight: 120
            color: surfaceColor
            border.color: borderColor
            border.width: 1

            ColumnLayout {
                anchors.fill: parent
                spacing: 0

                // Tab Bar
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 24
                    color: surfaceDark
                    border.color: borderColor
                    border.width: 1

                    // Fixed-width ribbon tabs container
                    Row {
                        anchors.left: parent.left
                        anchors.top: parent.top
                        anchors.bottom: parent.bottom
                        anchors.leftMargin: 2
                        spacing: 0

                        Repeater {
                            model: ["File", "Home", "Analysis", "Simulation", "Test", "Diagnostics", "Environment", "Hardware", "Tools", "Layout"]

                            Rectangle {
                                id: ribbonTab
                                width: Math.max(60, tabText.contentWidth + 16) // Fixed width based on content
                                height: parent.height

                                property bool isActive: tabBar.currentIndex === index
                                property bool isHovered: tabMouseArea.containsMouse

                                // Seamless connection to content panel
                                color: isActive ? surfaceLight : (isHovered ? primaryLight : "transparent")
                                border.color: isActive ? borderColor : "transparent"
                                border.width: isActive ? 1 : 0

                                // Remove bottom border for active tab to create seamless connection
                                Rectangle {
                                    anchors.bottom: parent.bottom
                                    anchors.left: parent.left
                                    anchors.right: parent.right
                                    height: 1
                                    color: isActive ? surfaceLight : "transparent"
                                    visible: isActive
                                }

                                Text {
                                    id: tabText
                                    anchors.centerIn: parent
                                    text: modelData
                                    font.pixelSize: 11
                                    color: ribbonTab.isActive ? textPrimary : (ribbonTab.isHovered ? textOnPrimary : textPrimary)
                                }

                                MouseArea {
                                    id: tabMouseArea
                                    anchors.fill: parent
                                    hoverEnabled: true
                                    onClicked: tabBar.currentIndex = index
                                }
                            }
                        }

                        // Spacer to fill remaining width
                        Item {
                            width: parent.parent.width - parent.width - parent.anchors.leftMargin
                            height: parent.height
                        }
                    }

                    // Hidden TabBar for state management
                    TabBar {
                        id: tabBar
                        visible: false
                        currentIndex: 0

                        Repeater {
                            model: 10
                            TabButton { }
                        }
                    }
                }

                // Ribbon Content
                StackLayout {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    currentIndex: tabBar.currentIndex

                    // File Tab
                    Rectangle {
                        color: surfaceLight
                        border.color: borderColor
                        border.width: 1
                        // Remove top border for seamless connection
                        Rectangle {
                            anchors.top: parent.top
                            anchors.left: parent.left
                            anchors.right: parent.right
                            height: 1
                            color: surfaceLight
                        }

                        Row {
                            anchors.left: parent.left
                            anchors.top: parent.top
                            anchors.margins: 8
                            spacing: 16

                            Column {
                                spacing: 4

                                Text {
                                    text: "Configuration"
                                    font.pixelSize: 10
                                    color: textSecondary
                                    font.weight: Font.Bold
                                }

                                Row {
                                    spacing: 4

                                    Button {
                                        text: "New"
                                        width: 60
                                        height: 50
                                        font.pixelSize: 9
                                        background: Rectangle {
                                            color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                            border.color: borderColor
                                            border.width: 1
                                            radius: 3
                                        }
                                        onClicked: console.log("New configuration")
                                    }

                                    Button {
                                        text: "Open"
                                        width: 60
                                        height: 50
                                        font.pixelSize: 9
                                        background: Rectangle {
                                            color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                            border.color: borderColor
                                            border.width: 1
                                            radius: 3
                                        }
                                        onClicked: console.log("Open configuration")
                                    }

                                    Button {
                                        text: "Save"
                                        width: 60
                                        height: 50
                                        font.pixelSize: 9
                                        background: Rectangle {
                                            color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                            border.color: borderColor
                                            border.width: 1
                                            radius: 3
                                        }
                                        onClicked: console.log("Save configuration")
                                    }
                                }
                            }
                        }
                    }

                    // Home Tab
                    Rectangle {
                        color: surfaceLight
                        border.color: borderColor
                        border.width: 1
                        // Remove top border for seamless connection
                        Rectangle {
                            anchors.top: parent.top
                            anchors.left: parent.left
                            anchors.right: parent.right
                            height: 1
                            color: surfaceLight
                        }

                        Row {
                            anchors.left: parent.left
                            anchors.top: parent.top
                            anchors.margins: 8
                            spacing: 16

                            Column {
                                spacing: 4

                                Text {
                                    text: "Simulation"
                                    font.pixelSize: 10
                                    color: textSecondary
                                    font.weight: Font.Bold
                                }

                                Row {
                                    spacing: 4

                                    Button {
                                        text: "Start"
                                        width: 60
                                        height: 50
                                        font.pixelSize: 9
                                        background: Rectangle {
                                            color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                            border.color: borderColor
                                            border.width: 1
                                            radius: 3
                                        }
                                        onClicked: console.log("Start simulation")
                                    }

                                    Button {
                                        text: "Stop"
                                        width: 60
                                        height: 50
                                        font.pixelSize: 9
                                        background: Rectangle {
                                            color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                            border.color: borderColor
                                            border.width: 1
                                            radius: 3
                                        }
                                        onClicked: console.log("Stop simulation")
                                    }
                                }
                            }

                            // Separator
                            Rectangle {
                                width: 1
                                height: 60
                                color: borderColor
                            }

                            Column {
                                spacing: 4

                                Text {
                                    text: "View"
                                    font.pixelSize: 10
                                    color: textSecondary
                                    font.weight: Font.Bold
                                }

                                Row {
                                    spacing: 4

                                    Button {
                                        text: "Zoom In"
                                        width: 60
                                        height: 50
                                        font.pixelSize: 9
                                        background: Rectangle {
                                            color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                            border.color: borderColor
                                            border.width: 1
                                            radius: 3
                                        }
                                        onClicked: console.log("Zoom In")
                                    }

                                    Button {
                                        text: "Zoom Out"
                                        width: 60
                                        height: 50
                                        font.pixelSize: 9
                                        background: Rectangle {
                                            color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                            border.color: borderColor
                                            border.width: 1
                                            radius: 3
                                        }
                                        onClicked: console.log("Zoom Out")
                                    }
                                }
                            }
                        }
                    }

                    // Analysis Tab
                    Rectangle {
                        color: surfaceLight
                        border.color: borderColor
                        border.width: 1
                        // Remove top border for seamless connection
                        Rectangle {
                            anchors.top: parent.top
                            anchors.left: parent.left
                            anchors.right: parent.right
                            height: 1
                            color: surfaceLight
                        }

                        ScrollView {
                            anchors.fill: parent

                            Row {
                                anchors.left: parent.left
                                anchors.top: parent.top
                                anchors.margins: 8
                                spacing: 16

                                // Configuration Group
                                Column {
                                    spacing: 4

                                    Text {
                                        text: "Configuration"
                                        font.pixelSize: 10
                                        color: textSecondary
                                        font.weight: Font.Bold
                                    }

                                    Button {
                                        text: "Measurement\nSetup"
                                        width: 80
                                        height: 50
                                        font.pixelSize: 9
                                        background: Rectangle {
                                            color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                            border.color: borderColor
                                            border.width: 1
                                            radius: 3
                                        }
                                        onClicked: {
                                            console.log("Opening Measurement Setup")
                                            // Will open Measurement Setup tool window
                                        }
                                    }
                                }

                                // Separator
                                Rectangle {
                                    width: 1
                                    height: 60
                                    color: borderColor
                                }

                                // Bus Analysis Group
                                Column {
                                    spacing: 4

                                    Text {
                                        text: "Bus Analysis"
                                        font.pixelSize: 10
                                        color: textSecondary
                                        font.weight: Font.Bold
                                    }

                                    Row {
                                        spacing: 4

                                        Button {
                                            text: "Trace"
                                            width: 60
                                            height: 50
                                            font.pixelSize: 9
                                            background: Rectangle {
                                                color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                                border.color: borderColor
                                                border.width: 1
                                                radius: 3
                                            }
                                            onClicked: {
                                                console.log("Opening Trace View")
                                                // Will open Trace tool window
                                            }
                                        }

                                        Button {
                                            text: "Graphics"
                                            width: 60
                                            height: 50
                                            font.pixelSize: 9
                                            background: Rectangle {
                                                color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                                border.color: borderColor
                                                border.width: 1
                                                radius: 3
                                            }
                                            onClicked: {
                                                console.log("Opening Graphics")
                                                // Will open Graphics tool window
                                            }
                                        }
                                    }
                                }

                                // Separator
                                Rectangle {
                                    width: 1
                                    height: 60
                                    color: borderColor
                                }

                                // More Analysis Group
                                Column {
                                    spacing: 4

                                    Text {
                                        text: "More Analysis"
                                        font.pixelSize: 10
                                        color: textSecondary
                                        font.weight: Font.Bold
                                    }

                                    Row {
                                        spacing: 4

                                        Button {
                                            text: "Statistics"
                                            width: 60
                                            height: 50
                                            font.pixelSize: 9
                                            background: Rectangle {
                                                color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                                border.color: borderColor
                                                border.width: 1
                                                radius: 3
                                            }
                                            onClicked: {
                                                console.log("Opening Statistics")
                                                // Will open Statistics tool window
                                            }
                                        }

                                        Button {
                                            text: "Logging"
                                            width: 60
                                            height: 50
                                            font.pixelSize: 9
                                            background: Rectangle {
                                                color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                                border.color: borderColor
                                                border.width: 1
                                                radius: 3
                                            }
                                            onClicked: {
                                                console.log("Opening Logging")
                                            }
                                        }
                                    }
                                }

                                // Separator
                                Rectangle {
                                    width: 1
                                    height: 60
                                    color: borderColor
                                }

                                // MOST Group
                                Column {
                                    spacing: 4

                                    Text {
                                        text: "MOST"
                                        font.pixelSize: 10
                                        color: textSecondary
                                        font.weight: Font.Bold
                                    }

                                    Row {
                                        spacing: 4

                                        Button {
                                            text: "MOST\nTrace"
                                            width: 60
                                            height: 50
                                            font.pixelSize: 9
                                            background: Rectangle {
                                                color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                                border.color: borderColor
                                                border.width: 1
                                                radius: 3
                                            }
                                            onClicked: {
                                                console.log("Opening MOST Trace")
                                            }
                                        }

                                        Button {
                                            text: "MOST\nConfig"
                                            width: 60
                                            height: 50
                                            font.pixelSize: 9
                                            background: Rectangle {
                                                color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                                border.color: borderColor
                                                border.width: 1
                                                radius: 3
                                            }
                                            onClicked: {
                                                console.log("Opening MOST Config")
                                            }
                                        }
                                    }
                                }

                                // Separator
                                Rectangle {
                                    width: 1
                                    height: 60
                                    color: borderColor
                                }

                                // Car2x Group
                                Column {
                                    spacing: 4

                                    Text {
                                        text: "Car2x"
                                        font.pixelSize: 10
                                        color: textSecondary
                                        font.weight: Font.Bold
                                    }

                                    Row {
                                        spacing: 4

                                        Button {
                                            text: "Car2x\nTrace"
                                            width: 60
                                            height: 50
                                            font.pixelSize: 9
                                            background: Rectangle {
                                                color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                                border.color: borderColor
                                                border.width: 1
                                                radius: 3
                                            }
                                            onClicked: {
                                                console.log("Opening Car2x Trace")
                                            }
                                        }

                                        Button {
                                            text: "Car2x\nConfig"
                                            width: 60
                                            height: 50
                                            font.pixelSize: 9
                                            background: Rectangle {
                                                color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                                border.color: borderColor
                                                border.width: 1
                                                radius: 3
                                            }
                                            onClicked: {
                                                console.log("Opening Car2x Config")
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // Simulation Tab
                    Rectangle {
                        color: surfaceLight
                        border.color: borderColor
                        border.width: 1
                        // Remove top border for seamless connection
                        Rectangle {
                            anchors.top: parent.top
                            anchors.left: parent.left
                            anchors.right: parent.right
                            height: 1
                            color: surfaceLight
                        }

                        Row {
                            anchors.left: parent.left
                            anchors.top: parent.top
                            anchors.margins: 8
                            spacing: 16

                            Column {
                                spacing: 4

                                Text {
                                    text: "Simulation Control"
                                    font.pixelSize: 10
                                    color: textSecondary
                                    font.weight: Font.Bold
                                }

                                Row {
                                    spacing: 4

                                    Button {
                                        text: "Reset"
                                        width: 60
                                        height: 50
                                        font.pixelSize: 9
                                        background: Rectangle {
                                            color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                            border.color: borderColor
                                            border.width: 1
                                            radius: 3
                                        }
                                        onClicked: console.log("Reset simulation")
                                    }

                                    Button {
                                        text: "Step"
                                        width: 60
                                        height: 50
                                        font.pixelSize: 9
                                        background: Rectangle {
                                            color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                            border.color: borderColor
                                            border.width: 1
                                            radius: 3
                                        }
                                        onClicked: console.log("Step simulation")
                                    }
                                }
                            }
                        }
                    }

                    // Test Tab
                    Rectangle {
                        color: surfaceLight
                        border.color: borderColor
                        border.width: 1
                        // Remove top border for seamless connection
                        Rectangle {
                            anchors.top: parent.top
                            anchors.left: parent.left
                            anchors.right: parent.right
                            height: 1
                            color: surfaceLight
                        }

                        Row {
                            anchors.left: parent.left
                            anchors.top: parent.top
                            anchors.margins: 8
                            spacing: 16

                            Column {
                                spacing: 4

                                Text {
                                    text: "Test Execution"
                                    font.pixelSize: 10
                                    color: textSecondary
                                    font.weight: Font.Bold
                                }

                                Row {
                                    spacing: 4

                                    Button {
                                        text: "Run Test"
                                        width: 70
                                        height: 50
                                        font.pixelSize: 9
                                        background: Rectangle {
                                            color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                            border.color: borderColor
                                            border.width: 1
                                            radius: 3
                                        }
                                        onClicked: console.log("Run Test")
                                    }

                                    Button {
                                        text: "Test Report"
                                        width: 70
                                        height: 50
                                        font.pixelSize: 9
                                        background: Rectangle {
                                            color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                            border.color: borderColor
                                            border.width: 1
                                            radius: 3
                                        }
                                        onClicked: console.log("Test Report")
                                    }
                                }
                            }
                        }
                    }

                    // Diagnostics Tab
                    Rectangle {
                        color: surfaceLight
                        border.color: borderColor
                        border.width: 1
                        // Remove top border for seamless connection
                        Rectangle {
                            anchors.top: parent.top
                            anchors.left: parent.left
                            anchors.right: parent.right
                            height: 1
                            color: surfaceLight
                        }

                        Row {
                            anchors.left: parent.left
                            anchors.top: parent.top
                            anchors.margins: 8
                            spacing: 16

                            Column {
                                spacing: 4

                                Text {
                                    text: "Diagnostics"
                                    font.pixelSize: 10
                                    color: textSecondary
                                    font.weight: Font.Bold
                                }

                                Row {
                                    spacing: 4

                                    Button {
                                        text: "UDS"
                                        width: 60
                                        height: 50
                                        font.pixelSize: 9
                                        background: Rectangle {
                                            color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                            border.color: borderColor
                                            border.width: 1
                                            radius: 3
                                        }
                                        onClicked: console.log("UDS")
                                    }

                                    Button {
                                        text: "OBD"
                                        width: 60
                                        height: 50
                                        font.pixelSize: 9
                                        background: Rectangle {
                                            color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                            border.color: borderColor
                                            border.width: 1
                                            radius: 3
                                        }
                                        onClicked: console.log("OBD")
                                    }
                                }
                            }
                        }
                    }

                    // Environment Tab
                    Rectangle {
                        color: surfaceLight
                        border.color: borderColor
                        border.width: 1
                        // Remove top border for seamless connection
                        Rectangle {
                            anchors.top: parent.top
                            anchors.left: parent.left
                            anchors.right: parent.right
                            height: 1
                            color: surfaceLight
                        }

                        Row {
                            anchors.left: parent.left
                            anchors.top: parent.top
                            anchors.margins: 8
                            spacing: 16

                            Column {
                                spacing: 4

                                Text {
                                    text: "Environment"
                                    font.pixelSize: 10
                                    color: textSecondary
                                    font.weight: Font.Bold
                                }

                                Button {
                                    text: "Variables"
                                    width: 80
                                    height: 50
                                    font.pixelSize: 9
                                    background: Rectangle {
                                        color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                        border.color: borderColor
                                        border.width: 1
                                        radius: 3
                                    }
                                    onClicked: console.log("Variables")
                                }
                            }
                        }
                    }

                    // Hardware Tab
                    Rectangle {
                        color: surfaceLight
                        border.color: borderColor
                        border.width: 1
                        // Remove top border for seamless connection
                        Rectangle {
                            anchors.top: parent.top
                            anchors.left: parent.left
                            anchors.right: parent.right
                            height: 1
                            color: surfaceLight
                        }

                        Row {
                            anchors.left: parent.left
                            anchors.top: parent.top
                            anchors.margins: 8
                            spacing: 16

                            Column {
                                spacing: 4

                                Text {
                                    text: "Hardware"
                                    font.pixelSize: 10
                                    color: textSecondary
                                    font.weight: Font.Bold
                                }

                                Button {
                                    text: "CAN Config"
                                    width: 80
                                    height: 50
                                    font.pixelSize: 9
                                    background: Rectangle {
                                        color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                        border.color: borderColor
                                        border.width: 1
                                        radius: 3
                                    }
                                    onClicked: console.log("CAN Config")
                                }
                            }
                        }
                    }

                    // Tools Tab
                    Rectangle {
                        color: surfaceLight
                        border.color: borderColor
                        border.width: 1
                        // Remove top border for seamless connection
                        Rectangle {
                            anchors.top: parent.top
                            anchors.left: parent.left
                            anchors.right: parent.right
                            height: 1
                            color: surfaceLight
                        }

                        Row {
                            anchors.left: parent.left
                            anchors.top: parent.top
                            anchors.margins: 8
                            spacing: 16

                            Column {
                                spacing: 4

                                Text {
                                    text: "Tools"
                                    font.pixelSize: 10
                                    color: textSecondary
                                    font.weight: Font.Bold
                                }

                                Button {
                                    text: "Options"
                                    width: 70
                                    height: 50
                                    font.pixelSize: 9
                                    background: Rectangle {
                                        color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                        border.color: borderColor
                                        border.width: 1
                                        radius: 3
                                    }
                                    onClicked: console.log("Options")
                                }
                            }
                        }
                    }

                    // Layout Tab
                    Rectangle {
                        color: surfaceLight
                        border.color: borderColor
                        border.width: 1
                        // Remove top border for seamless connection
                        Rectangle {
                            anchors.top: parent.top
                            anchors.left: parent.left
                            anchors.right: parent.right
                            height: 1
                            color: surfaceLight
                        }

                        Row {
                            anchors.left: parent.left
                            anchors.top: parent.top
                            anchors.margins: 8
                            spacing: 16

                            Column {
                                spacing: 4

                                Text {
                                    text: "Layout"
                                    font.pixelSize: 10
                                    color: textSecondary
                                    font.weight: Font.Bold
                                }

                                Row {
                                    spacing: 4

                                    Button {
                                        text: "Save Layout"
                                        width: 80
                                        height: 50
                                        font.pixelSize: 9
                                        background: Rectangle {
                                            color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                            border.color: borderColor
                                            border.width: 1
                                            radius: 3
                                        }
                                        onClicked: console.log("Save Layout")
                                    }

                                    Button {
                                        text: "Load Layout"
                                        width: 80
                                        height: 50
                                        font.pixelSize: 9
                                        background: Rectangle {
                                            color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                                            border.color: borderColor
                                            border.width: 1
                                            radius: 3
                                        }
                                        onClicked: console.log("Load Layout")
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // Desktop Tabs
        Rectangle {
            id: desktopTabs
            Layout.fillWidth: true
            Layout.preferredHeight: 32
            color: surfaceDark
            border.color: borderColor
            border.width: 1

            // Fixed-width desktop tabs container
            Row {
                anchors.left: parent.left
                anchors.top: parent.top
                anchors.bottom: parent.bottom
                anchors.leftMargin: 4
                anchors.topMargin: 2
                anchors.bottomMargin: 2
                spacing: 2

                Repeater {
                    model: ["Trace", "Configuration", "Analysis"]

                    Rectangle {
                        id: desktopTab
                        width: Math.max(80, desktopTabText.contentWidth + 20) // Fixed width based on content
                        height: parent.height

                        property bool isActive: desktopTabBar.currentIndex === index
                        property bool isHovered: desktopTabMouseArea.containsMouse

                        color: isActive ? primaryColor : (isHovered ? primaryLight : "transparent")
                        border.color: isActive ? borderColor : "transparent"
                        border.width: isActive ? 1 : 0
                        radius: 3

                        Text {
                            id: desktopTabText
                            anchors.centerIn: parent
                            text: modelData
                            font.pixelSize: 11
                            color: desktopTab.isActive ? textOnPrimary : textPrimary
                        }

                        MouseArea {
                            id: desktopTabMouseArea
                            anchors.fill: parent
                            hoverEnabled: true
                            acceptedButtons: Qt.LeftButton | Qt.RightButton

                            onClicked: {
                                if (mouse.button === Qt.LeftButton) {
                                    desktopTabBar.currentIndex = index
                                } else if (mouse.button === Qt.RightButton) {
                                    tabContextMenu.popup()
                                }
                            }
                        }
                    }
                }

                // Spacer to fill remaining width
                Item {
                    width: Math.max(0, parent.parent.width - parent.width - parent.anchors.leftMargin - 8)
                    height: parent.height
                }
            }

            // Hidden TabBar for state management
            TabBar {
                id: desktopTabBar
                visible: false
                currentIndex: 0

                Repeater {
                    model: 3
                    TabButton { }
                }
            }

            // Context menu
            Menu {
                id: tabContextMenu

                MenuItem {
                    text: "New Tab"
                    onTriggered: console.log("New tab")
                }

                MenuItem {
                    text: "Close Tab"
                    onTriggered: console.log("Close tab")
                }

                MenuItem {
                    text: "Rename Tab"
                    onTriggered: console.log("Rename tab")
                }
            }
        }

        // Main Content Area (Dock Area)
        StackLayout {
            id: dockArea
            Layout.fillWidth: true
            Layout.fillHeight: true
            currentIndex: desktopTabBar.currentIndex

            // Trace workspace
            Rectangle {
                color: backgroundColor

                Text {
                    anchors.centerIn: parent
                    text: "Trace Workspace\nClick Analysis → Trace to open Trace View"
                    color: textSecondary
                    font.pixelSize: 14
                    horizontalAlignment: Text.AlignHCenter
                }
            }

            // Configuration workspace
            Rectangle {
                color: backgroundColor

                Text {
                    anchors.centerIn: parent
                    text: "Configuration Workspace\nClick Analysis → Measurement Setup to configure"
                    color: textSecondary
                    font.pixelSize: 14
                    horizontalAlignment: Text.AlignHCenter
                }
            }

            // Analysis workspace
            Rectangle {
                color: backgroundColor

                Text {
                    anchors.centerIn: parent
                    text: "Analysis Workspace\nClick Analysis → Statistics or Graphics to analyze data"
                    color: textSecondary
                    font.pixelSize: 14
                    horizontalAlignment: Text.AlignHCenter
                }
            }
        }

        // Status Bar
        Rectangle {
            id: statusBar
            Layout.fillWidth: true
            Layout.preferredHeight: 24
            color: surfaceDark
            border.color: borderColor
            border.width: 1

            RowLayout {
                anchors.fill: parent
                anchors.margins: 4

                // Left: App state
                Text {
                    text: "Ready"
                    color: textSecondary
                    font.pixelSize: 11
                }

                // Spacer
                Item {
                    Layout.fillWidth: true
                }

                // Center: Progress
                ProgressBar {
                    Layout.preferredWidth: 200
                    value: 0.0
                    visible: false
                    background: Rectangle {
                        color: surfaceColor
                        border.color: borderColor
                        border.width: 1
                        radius: 2
                    }
                    contentItem: Rectangle {
                        color: primaryColor
                        radius: 2
                    }
                }

                // Spacer
                Item {
                    Layout.fillWidth: true
                }

                // Right: CAN bitrate and clock
                Row {
                    spacing: 12

                    Text {
                        text: "CAN: 500 kbit/s"
                        color: textSecondary
                        font.pixelSize: 11
                    }

                    Text {
                        id: clockText
                        color: textSecondary
                        font.pixelSize: 11

                        Timer {
                            interval: 1000
                            running: true
                            repeat: true
                            onTriggered: {
                                clockText.text = Qt.formatDateTime(new Date(), "hh:mm:ss")
                            }
                        }

                        Component.onCompleted: {
                            text = Qt.formatDateTime(new Date(), "hh:mm:ss")
                        }
                    }
                }
            }
        }
    }

    // Configuration Settings Dialog
    Dialog {
        id: configSettingsDialog
        title: "Configuration Settings"
        width: 400
        height: 300
        modal: true
        anchors.centerIn: parent

        background: Rectangle {
            color: surfaceLight
            border.color: borderColor
            border.width: 1
            radius: 4
        }

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 16
            spacing: 12

            Text {
                text: "Configuration Settings"
                font.pixelSize: 16
                font.weight: Font.Bold
                color: textPrimary
            }

            GroupBox {
                title: "General Settings"
                Layout.fillWidth: true

                ColumnLayout {
                    anchors.fill: parent

                    CheckBox {
                        text: "Auto-save configuration"
                        checked: true
                    }

                    CheckBox {
                        text: "Show tooltips"
                        checked: true
                    }

                    CheckBox {
                        text: "Enable logging"
                        checked: false
                    }
                }
            }

            Item {
                Layout.fillHeight: true
            }

            RowLayout {
                Layout.fillWidth: true

                Item {
                    Layout.fillWidth: true
                }

                Button {
                    text: "OK"
                    background: Rectangle {
                        color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                        border.color: borderColor
                        border.width: 1
                        radius: 3
                    }
                    onClicked: configSettingsDialog.close()
                }

                Button {
                    text: "Cancel"
                    background: Rectangle {
                        color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                        border.color: borderColor
                        border.width: 1
                        radius: 3
                    }
                    onClicked: configSettingsDialog.close()
                }
            }
        }
    }

    // Customize Ribbon Settings Dialog
    Dialog {
        id: customizeRibbonDialog
        title: "Customize Ribbon Settings"
        width: 500
        height: 400
        modal: true
        anchors.centerIn: parent

        background: Rectangle {
            color: surfaceLight
            border.color: borderColor
            border.width: 1
            radius: 4
        }

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 16
            spacing: 12

            Text {
                text: "Customize Ribbon Settings"
                font.pixelSize: 16
                font.weight: Font.Bold
                color: textPrimary
            }

            GroupBox {
                title: "Ribbon Options"
                Layout.fillWidth: true
                Layout.fillHeight: true

                ColumnLayout {
                    anchors.fill: parent

                    CheckBox {
                        text: "Show ribbon"
                        checked: true
                    }

                    CheckBox {
                        text: "Minimize ribbon"
                        checked: false
                    }

                    CheckBox {
                        text: "Show Quick Access Toolbar"
                        checked: true
                    }

                    Text {
                        text: "Available Tabs:"
                        font.weight: Font.Bold
                        color: textPrimary
                    }

                    ScrollView {
                        Layout.fillWidth: true
                        Layout.fillHeight: true

                        Column {
                            spacing: 4

                            CheckBox { text: "File"; checked: true }
                            CheckBox { text: "Home"; checked: true }
                            CheckBox { text: "Analysis"; checked: true }
                            CheckBox { text: "Simulation"; checked: true }
                            CheckBox { text: "Test"; checked: true }
                            CheckBox { text: "Diagnostics"; checked: true }
                            CheckBox { text: "Environment"; checked: true }
                            CheckBox { text: "Hardware"; checked: true }
                            CheckBox { text: "Tools"; checked: true }
                            CheckBox { text: "Layout"; checked: true }
                        }
                    }
                }
            }

            RowLayout {
                Layout.fillWidth: true

                Item {
                    Layout.fillWidth: true
                }

                Button {
                    text: "OK"
                    background: Rectangle {
                        color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                        border.color: borderColor
                        border.width: 1
                        radius: 3
                    }
                    onClicked: customizeRibbonDialog.close()
                }

                Button {
                    text: "Cancel"
                    background: Rectangle {
                        color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                        border.color: borderColor
                        border.width: 1
                        radius: 3
                    }
                    onClicked: customizeRibbonDialog.close()
                }
            }
        }
    }
}

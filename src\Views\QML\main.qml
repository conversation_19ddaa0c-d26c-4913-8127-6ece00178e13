/**
 * @file main.qml
 * @brief Main window for CANoeLite application (minimal test version)
 * <AUTHOR> Team
 * @date 2025
 */

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "Tools"
import QtQuick.Window 2.15

Window {
    id: mainWindow
    flags: Qt.FramelessWindowHint | Qt.Window
    title: "CANoeLite - Vector CANoe Style Desktop Application"
    width: 1200
    height: 800
    minimumWidth: 800
    minimumHeight: 600
    visible: true

    // Theme colors
    readonly property color primaryColor: "#7A1E2A"        // Bordeaux red
    readonly property color primaryLight: "#A52A3A"        // Lighter bordeaux
    readonly property color surfaceColor: "#F2F2F2"        // Light gray
    readonly property color surfaceLight: "#FFFFFF"        // White
    readonly property color surfaceDark: "#E0E0E0"         // Darker gray
    readonly property color backgroundColor: "#F8F8F8"     // Very light gray
    readonly property color borderColor: "#CCCCCC"         // Border gray
    readonly property color textPrimary: "#212121"         // Dark text
    readonly property color textSecondary: "#757575"       // Medium text
    readonly property color textOnPrimary: "#FFFFFF"       // White text on primary

    // Standardized RibbonButton component
    component RibbonButton: Button {
        property string buttonText: ""
        property string iconText: "📄"

        width: 60
        height: 60

        background: Rectangle {
            color: parent.pressed ? mainWindow.primaryColor : (parent.hovered ? mainWindow.primaryLight : mainWindow.surfaceColor)
            border.color: mainWindow.borderColor
            border.width: 1
            radius: 3
        }

        contentItem: Item {
            anchors.fill: parent

            Column {
                anchors.centerIn: parent
                spacing: 2
                width: parent.width - 8

                Text {
                    anchors.horizontalCenter: parent.horizontalCenter
                    text: parent.parent.parent.iconText
                    font.pixelSize: 16
                    color: parent.parent.parent.pressed ? mainWindow.textOnPrimary : mainWindow.textPrimary
                    horizontalAlignment: Text.AlignHCenter
                }

                Text {
                    anchors.horizontalCenter: parent.horizontalCenter
                    text: parent.parent.parent.buttonText
                    font.pixelSize: 8
                    color: parent.parent.parent.pressed ? mainWindow.textOnPrimary : mainWindow.textPrimary
                    horizontalAlignment: Text.AlignHCenter
                    wrapMode: Text.WordWrap
                    width: parent.width
                    maximumLineCount: 2
                    elide: Text.ElideRight
                }
            }
        }
    }

    // Main layout
    ColumnLayout {
        anchors.fill: parent
        spacing: 0

        // Quick Access Toolbar
        Rectangle {
            id: quickAccessToolbar
            Layout.fillWidth: true
            Layout.preferredHeight: 36
            color: surfaceColor
            border.color: borderColor
            border.width: 1

            DragHandler {
                target:  null                      // don’t move the rectangle itself
                acceptedButtons: Qt.LeftButton
                grabPermissions: PointerHandler.CanTakeOverFromAnything
                onActiveChanged: if (active) mainWindow.startSystemMove()
            }

            RowLayout {
                anchors.fill: parent
                anchors.margins: 4
                spacing: 4

                // Group 1: Window controls dropdown
                ToolButton {
                    width: 24
                    height: 24
                    text: "🗔"
                    ToolTip.text: "Window Controls"
                    background: Rectangle {
                        color: parent.hovered ? primaryLight : "transparent"
                        radius: 2
                    }
                    onClicked: windowControlsMenu.open()

                    Menu {
                        id: windowControlsMenu

                        MenuItem {
                            text: "Restore"
                            onTriggered: mainWindow.showNormal()
                        }

                        MenuItem {
                            text: "Minimize"
                            onTriggered: mainWindow.showMinimized()
                        }

                        MenuItem {
                            text: "Maximize"
                            onTriggered: mainWindow.showMaximized()
                        }

                        MenuSeparator {}

                        MenuItem {
                            text: "Close Application"
                            onTriggered: Qt.quit()
                        }
                    }
                }

                // Separator
                Row {
                    spacing: 2
                    Rectangle {
                        width: 1
                        height: parent.height * 0.6
                        color: borderColor
                    }
                }

                // Group 2: Start/Stop controls
                Row {
                    spacing: 2

                    ToolButton {
                        width: 24
                        height: 24
                        text: "⚡"
                        ToolTip.text: "Start"
                        ToolTip.visible: hovered
                        background: Rectangle {
                            color: parent.hovered ? primaryLight : "transparent"
                            radius: 2
                        }
                        onClicked: console.log("Start clicked")
                    }

                    ToolButton {
                        width: 24
                        height: 24
                        text: "⛔"
                        ToolTip.text: "Stop"
                        ToolTip.visible: hovered
                        background: Rectangle {
                            color: parent.hovered ? primaryLight : "transparent"
                            radius: 2
                        }
                        onClicked: console.log("Stop clicked")
                    }
                }

                // Separator
                Row {
                    spacing: 2
                    Rectangle {
                        width: 1
                        height: parent.height * 0.6
                        color: borderColor
                    }
                }

                // Group 3: File operations
                Row {
                    spacing: 2

                    ToolButton {
                        width: 30
                        height: 24
                        text: "📁"
                        ToolTip.text: "Load configuration"
                        ToolTip.visible: hovered
                        background: Rectangle {
                            color: parent.hovered ? primaryLight : "transparent"
                            radius: 2
                        }
                        onClicked: console.log("Open clicked")
                    }

                    ToolButton {
                        width: 28
                        height: 24
                        text: "💾"
                        ToolTip.text: "Save Configuration"
                        ToolTip.visible: hovered
                        background: Rectangle {
                            color: parent.hovered ? primaryLight : "transparent"
                            radius: 2
                        }
                        onClicked: console.log("Save clicked")
                    }
                }

                // Spacer to push window controls to the right
                Item {
                    Layout.fillWidth:  true
                }

                // Group 6: Window controls
                Row {
                    spacing: 2

                    ToolButton {
                        width: 24
                        height: 24
                        text: "🗕"
                        ToolTip.text: "Minimize"
                        background: Rectangle {
                            color: parent.hovered ? primaryLight : "transparent"
                            radius: 2
                        }
                        onClicked: mainWindow.showMinimized()
                    }

                    ToolButton {
                        id: maximizeButton
                        width: 24
                        height: 24
                        text: "🗖"
                        ToolTip.text: "Maximize"
                        background: Rectangle {
                            color: parent.hovered ? primaryLight : "transparent"
                            radius: 2
                        }
                        onClicked: {
                            if (mainWindow.visibility === Window.Maximized) {
                                mainWindow.showNormal()
                            } else {
                                mainWindow.showMaximized()
                            }
                        }
                    }

                    ToolButton {
                        width: 24
                        height: 24
                        text: "✕"
                        ToolTip.text: "Close"
                        background: Rectangle {
                            color: parent.hovered ? primaryLight : "transparent"
                            radius: 2
                        }
                        onClicked: Qt.quit()
                    }
                }
            }
        }

        // Ribbon Interface
        Rectangle {
            id: ribbon
            Layout.fillWidth: true
            Layout.preferredHeight: 120
            color: surfaceColor
            border.color: borderColor
            border.width: 1

            ColumnLayout {
                anchors.fill: parent
                spacing: 0

                // Fixed-width ribbon tabs
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 24
                    color: surfaceDark
                    border.color: borderColor
                    border.width: 1

                    Row {
                        anchors.left: parent.left
                        anchors.top: parent.top
                        anchors.bottom: parent.bottom
                        anchors.leftMargin: 2
                        spacing: 0

                        Repeater {
                            model: ["File", "Home", "Analysis", "Simulation", "Test", "Diagnostics", "Environment", "Hardware", "Tools", "Layout"]

                            Rectangle {
                                id: ribbonTab
                                width: Math.min(85, Math.max(55, tabText.contentWidth + 12)) // Fixed width to prevent overlap
                                height: parent.height

                                property bool isActive: tabBar.currentIndex === index
                                property bool isHovered: tabMouseArea.containsMouse

                                // Seamless connection to content panel
                                color: isActive ? surfaceLight : (isHovered ? primaryLight : "transparent")
                                border.color: isActive ? borderColor : "transparent"
                                border.width: isActive ? 1 : 0

                                // Remove bottom border for seamless connection
                                Rectangle {
                                    anchors.bottom: parent.bottom
                                    anchors.left: parent.left
                                    anchors.right: parent.right
                                    height: 1
                                    color: isActive ? surfaceLight : "transparent"
                                    visible: isActive
                                }

                                Text {
                                    id: tabText
                                    anchors.centerIn: parent
                                    text: modelData
                                    font.pixelSize: 10 // Reduced font size to fit better
                                    color: ribbonTab.isActive ? textPrimary : (ribbonTab.isHovered ? textOnPrimary : textPrimary)
                                    elide: Text.ElideRight // Truncate if too long
                                }

                                MouseArea {
                                    id: tabMouseArea
                                    anchors.fill: parent
                                    hoverEnabled: true
                                    onClicked: tabBar.currentIndex = index
                                }
                            }
                        }
                    }

                    // Hidden TabBar for state management
                    TabBar {
                        id: tabBar
                        visible: false
                        currentIndex: 0

                        Repeater {
                            model: 10
                            TabButton { }
                        }
                    }
                }

                // Ribbon Content with seamless styling
                StackLayout {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    currentIndex: tabBar.currentIndex

                    // File Tab
                    Rectangle {
                        color: surfaceLight
                        border.color: borderColor
                        border.width: 1
                        // Remove top border for seamless connection
                        Rectangle {
                            anchors.top: parent.top
                            anchors.left: parent.left
                            anchors.right: parent.right
                            height: 1
                            color: surfaceLight
                        }

                        Text {
                            anchors.centerIn: parent
                            text: "Configuration Content"
                            font.pixelSize: 14
                            color: textSecondary
                        }
                    }

                    // Home Tab
                    Rectangle {
                        color: surfaceLight
                        border.color: borderColor
                        border.width: 1
                        Rectangle {
                            anchors.top: parent.top
                            anchors.left: parent.left
                            anchors.right: parent.right
                            height: 1
                            color: surfaceLight
                        }

                        Text {
                            anchors.centerIn: parent
                            text: "Home Content"
                            font.pixelSize: 14
                            color: textSecondary
                        }
                    }

                    // Analysis Tab
                    Rectangle {
                        color: surfaceLight
                        border.color: borderColor
                        border.width: 1
                        Rectangle {
                            anchors.top: parent.top
                            anchors.left: parent.left
                            anchors.right: parent.right
                            height: 1
                            color: surfaceLight
                        }

                        Row {
                            anchors.left: parent.left
                            anchors.top: parent.top
                            anchors.margins: 8
                            spacing: 16

                            Column {
                                spacing: 4

                                Text {
                                    text: "Configuration"
                                    font.pixelSize: 10
                                    color: textSecondary
                                    font.weight: Font.Bold
                                }

                                RibbonButton {
                                    buttonText: "Measurement\nSetup"
                                    iconText: "⚙️"
                                    onClicked: {
                                        console.log("Opening Measurement Setup")
                                        // TODO: Integrate with dock management system
                                        // For now, create a simple window for demonstration
                                        var measurementSetupWindow = measurementSetupComponent.createObject(mainWindow)
                                        if (measurementSetupWindow) {
                                            measurementSetupWindow.show()
                                        }
                                    }
                                }
                            }

                            Rectangle { width: 1; height: 60; color: borderColor }

                            Column {
                                spacing: 4

                                Text {
                                    text: "Bus Analysis"
                                    font.pixelSize: 10
                                    color: textSecondary
                                    font.weight: Font.Bold
                                }

                                Row {
                                    spacing: 4

                                    RibbonButton {
                                        buttonText: "Trace"
                                        iconText: "📊"
                                        onClicked: console.log("Opening Trace View")
                                    }

                                    RibbonButton {
                                        buttonText: "Graphics"
                                        iconText: "📈"
                                        onClicked: console.log("Opening Graphics")
                                    }
                                }
                            }

                            Rectangle { width: 1; height: 60; color: borderColor }

                            Column {
                                spacing: 4

                                Text {
                                    text: "More Analysis"
                                    font.pixelSize: 10
                                    color: textSecondary
                                    font.weight: Font.Bold
                                }

                                Row {
                                    spacing: 4

                                    RibbonButton {
                                        buttonText: "Statistics"
                                        iconText: "📋"
                                        onClicked: console.log("Opening Statistics")
                                    }

                                    RibbonButton {
                                        buttonText: "Logging"
                                        iconText: "📝"
                                        onClicked: console.log("Opening Logging")
                                    }
                                }
                            }
                        }
                    }

                    // Placeholder tabs for remaining ribbon tabs
                    Repeater {
                        model: 7 // Simulation, Test, Diagnostics, Environment, Hardware, Tools, Layout

                        Rectangle {
                            color: surfaceLight
                            border.color: borderColor
                            border.width: 1
                            Rectangle {
                                anchors.top: parent.top
                                anchors.left: parent.left
                                anchors.right: parent.right
                                height: 1
                                color: surfaceLight
                            }

                            Text {
                                anchors.centerIn: parent
                                text: ["Simulation", "Test", "Diagnostics", "Environment", "Hardware", "Tools", "Layout"][index] + " Content"
                                color: textSecondary
                                font.pixelSize: 14
                            }
                        }
                    }
                }
            }
        }

        // Fixed-width Desktop Tabs
        Rectangle {
            id: desktopTabs
            Layout.fillWidth: true
            Layout.preferredHeight: 32
            color: surfaceDark
            border.color: borderColor
            border.width: 1

            Row {
                anchors.left: parent.left
                anchors.top: parent.top
                anchors.bottom: parent.bottom
                anchors.leftMargin: 4
                anchors.topMargin: 2
                anchors.bottomMargin: 2
                spacing: 2

                Repeater {
                    model: ["Trace", "Configuration", "Analysis"]

                    Rectangle {
                        id: desktopTab
                        width: Math.max(80, desktopTabText.contentWidth + 20) // Fixed width based on content
                        height: parent.height

                        property bool isActive: desktopTabBar.currentIndex === index
                        property bool isHovered: desktopTabMouseArea.containsMouse

                        color: isActive ? primaryColor : (isHovered ? primaryLight : "transparent")
                        border.color: isActive ? borderColor : "transparent"
                        border.width: isActive ? 1 : 0
                        radius: 3

                        Text {
                            id: desktopTabText
                            anchors.centerIn: parent
                            text: modelData
                            font.pixelSize: 11
                            color: desktopTab.isActive ? textOnPrimary : textPrimary
                        }

                        MouseArea {
                            id: desktopTabMouseArea
                            anchors.fill: parent
                            hoverEnabled: true
                            onClicked: desktopTabBar.currentIndex = index
                        }
                    }
                }
            }

            TabBar {
                id: desktopTabBar
                visible: false
                currentIndex: 0

                Repeater {
                    model: 3
                    TabButton { }
                }
            }
        }

        // Main Content Area
        StackLayout {
            id: dockArea
            Layout.fillWidth: true
            Layout.fillHeight: true
            currentIndex: desktopTabBar.currentIndex

            Rectangle {
                color: backgroundColor
                Text {
                    anchors.centerIn: parent
                    text: "Trace Workspace"
                    color: textSecondary
                    font.pixelSize: 16
                }
            }

            Rectangle {
                color: backgroundColor
                Text {
                    anchors.centerIn: parent
                    text: "Configuration Workspace"
                    color: textSecondary
                    font.pixelSize: 16
                }
            }

            Rectangle {
                color: backgroundColor
                Text {
                    anchors.centerIn: parent
                    text: "Analysis Workspace"
                    color: textSecondary
                    font.pixelSize: 16
                }
            }
        }

        // Status Bar
        Rectangle {
            id: statusBar
            Layout.fillWidth: true
            Layout.preferredHeight: 24
            color: surfaceDark
            border.color: borderColor
            border.width: 1

            RowLayout {
                anchors.fill: parent
                anchors.margins: 4

                Text {
                    text: "Ready"
                    color: textSecondary
                    font.pixelSize: 11
                }

                Item {
                    Layout.fillWidth: true
                }

                Text {
                    text: "CANoeLite - Fixed Version"
                    color: textSecondary
                    font.pixelSize: 11
                }
            }
        }
    }

    // Configuration Settings Dialog
    Dialog {
        id: configDialog
        title: "Configuration Settings"
        width: 400
        height: 300
        anchors.centerIn: parent
        modal: true

        background: Rectangle {
            color: surfaceLight
            border.color: borderColor
            border.width: 1
            radius: 4
        }

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 16
            spacing: 16

            Text {
                text: "General Settings"
                font.pixelSize: 12
                font.weight: Font.Bold
                color: textPrimary
            }

            GroupBox {
                Layout.fillWidth: true
                title: "Application Settings"

                background: Rectangle {
                    color: surfaceColor
                    border.color: borderColor
                    border.width: 1
                    radius: 3
                }

                ColumnLayout {
                    anchors.fill: parent
                    spacing: 8

                    CheckBox {
                        text: "Auto-save configuration"
                        checked: true
                    }

                    CheckBox {
                        text: "Show tooltips"
                        checked: true
                    }

                    CheckBox {
                        text: "Enable logging"
                        checked: false
                    }
                }
            }

            Item {
                Layout.fillHeight: true
            }

            RowLayout {
                Layout.fillWidth: true

                Item {
                    Layout.fillWidth: true
                }

                Button {
                    text: "OK"
                    background: Rectangle {
                        color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                        border.color: borderColor
                        border.width: 1
                        radius: 3
                    }
                    onClicked: {
                        console.log("Configuration saved")
                        configDialog.close()
                    }
                }

                Button {
                    text: "Cancel"
                    background: Rectangle {
                        color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                        border.color: borderColor
                        border.width: 1
                        radius: 3
                    }
                    onClicked: configDialog.close()
                }
            }
        }
    }

    // Customize Ribbon Settings Dialog
    Dialog {
        id: customizeDialog
        title: "Customize Ribbon Settings"
        width: 450
        height: 350
        anchors.centerIn: parent
        modal: true

        background: Rectangle {
            color: surfaceLight
            border.color: borderColor
            border.width: 1
            radius: 4
        }

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 16
            spacing: 16

            Text {
                text: "Select Ribbon Tabs to Display"
                font.pixelSize: 12
                font.weight: Font.Bold
                color: textPrimary
            }

            ScrollView {
                Layout.fillWidth: true
                Layout.fillHeight: true

                ColumnLayout {
                    width: parent.width
                    spacing: 8

                    Repeater {
                        model: ["File", "Home", "Analysis", "Simulation", "Test", "Diagnostics", "Environment", "Hardware", "Tools", "Layout"]

                        CheckBox {
                            text: modelData
                            checked: true
                            Layout.fillWidth: true
                        }
                    }
                }
            }

            RowLayout {
                Layout.fillWidth: true

                Item {
                    Layout.fillWidth: true
                }

                Button {
                    text: "OK"
                    background: Rectangle {
                        color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                        border.color: borderColor
                        border.width: 1
                        radius: 3
                    }
                    onClicked: {
                        console.log("Ribbon customization saved")
                        customizeDialog.close()
                    }
                }

                Button {
                    text: "Cancel"
                    background: Rectangle {
                        color: parent.pressed ? primaryColor : (parent.hovered ? primaryLight : surfaceColor)
                        border.color: borderColor
                        border.width: 1
                        radius: 3
                    }
                    onClicked: customizeDialog.close()
                }
            }
        }
    }

    // Component for creating measurement setup tool windows
    Component {
        id: measurementSetupComponent

        ApplicationWindow {
            id: measurementSetupWindow
            title: "Measurement Setup"
            width: 800
            height: 600
            minimumWidth: 600
            minimumHeight: 400

            MeasurementSetup {
                anchors.fill: parent
            }
        }
    }
}

/**
 * @file main.qml
 * @brief Main window for CANoeLite application
 * <AUTHOR> Team
 * @date 2025
 */

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Window 2.15
import CANoeLite 1.0
import "Ribbon"

ApplicationWindow {
    id: mainWindow
    
    title: "CANoeLite - Vector CANoe Style Desktop Application"
    width: 1200
    height: 800
    minimumWidth: 800
    minimumHeight: 600
    visible: true
    
    // Remove default menu bar and title bar for custom implementation
    flags: Qt.Window | Qt.CustomizeWindowHint | Qt.WindowTitleHint | Qt.WindowSystemMenuHint | Qt.WindowMinMaxButtonsHint | Qt.WindowCloseButtonHint
    
    // Main layout
    ColumnLayout {
        anchors.fill: parent
        spacing: 0
        
        // Quick Access Toolbar
        Rectangle {
            id: quickAccessToolbar
            Layout.fillWidth: true
            Layout.preferredHeight: Theme.sizing.quickAccessHeight
            color: Theme.colors.surface
            border.color: Theme.colors.border
            border.width: 1
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: Theme.spacing.sm
                spacing: Theme.spacing.sm
                
                // Group 1: Window controls
                Row {
                    spacing: Theme.spacing.xs
                    
                    ToolButton {
                        width: Theme.sizing.quickAccessButtonSize
                        height: Theme.sizing.quickAccessButtonSize
                        text: "🗗"
                        ToolTip.text: "Restore"
                        onClicked: mainWindow.showNormal()
                    }
                    
                    ToolButton {
                        width: Theme.sizing.quickAccessButtonSize
                        height: Theme.sizing.quickAccessButtonSize
                        text: "🗕"
                        ToolTip.text: "Minimize"
                        onClicked: mainWindow.showMinimized()
                    }
                    
                    ToolButton {
                        width: Theme.sizing.quickAccessButtonSize
                        height: Theme.sizing.quickAccessButtonSize
                        text: "🗖"
                        ToolTip.text: "Maximize"
                        onClicked: mainWindow.showMaximized()
                    }
                    
                    ToolButton {
                        width: Theme.sizing.quickAccessButtonSize
                        height: Theme.sizing.quickAccessButtonSize
                        text: "🗙"
                        ToolTip.text: "Close"
                        onClicked: Qt.quit()
                    }
                }
                
                // Separator
                Rectangle {
                    width: 1
                    height: parent.height * 0.6
                    color: Theme.colors.border
                }
                
                // Group 2: Start/Stop controls
                Row {
                    spacing: Theme.spacing.xs
                    
                    ToolButton {
                        width: Theme.sizing.quickAccessButtonSize
                        height: Theme.sizing.quickAccessButtonSize
                        text: "▶"
                        ToolTip.text: "Start"
                        onClicked: MainViewModel.startSimulation()
                    }
                    
                    ToolButton {
                        width: Theme.sizing.quickAccessButtonSize
                        height: Theme.sizing.quickAccessButtonSize
                        text: "⏹"
                        ToolTip.text: "Stop"
                        onClicked: MainViewModel.stopSimulation()
                    }
                }
                
                // Separator
                Rectangle {
                    width: 1
                    height: parent.height * 0.6
                    color: Theme.colors.border
                }
                
                // Group 3: Configuration controls
                Row {
                    spacing: Theme.spacing.xs
                    
                    ToolButton {
                        width: Theme.sizing.quickAccessButtonSize
                        height: Theme.sizing.quickAccessButtonSize
                        text: "📁"
                        ToolTip.text: "Load Configuration"
                        onClicked: MainViewModel.loadConfiguration()
                    }
                    
                    ToolButton {
                        width: Theme.sizing.quickAccessButtonSize
                        height: Theme.sizing.quickAccessButtonSize
                        text: "💾"
                        ToolTip.text: "Save Configuration"
                        onClicked: MainViewModel.saveConfiguration()
                    }
                    
                    ToolButton {
                        width: Theme.sizing.quickAccessButtonSize
                        height: Theme.sizing.quickAccessButtonSize
                        text: "📄"
                        ToolTip.text: "Save As Configuration"
                        onClicked: MainViewModel.saveAsConfiguration()
                    }
                }
                
                // Separator
                Rectangle {
                    width: 1
                    height: parent.height * 0.6
                    color: Theme.colors.border
                }
                
                // Group 4: Settings
                ToolButton {
                    width: Theme.sizing.quickAccessButtonSize
                    height: Theme.sizing.quickAccessButtonSize
                    text: "⚙"
                    ToolTip.text: "Settings"
                    onClicked: MainViewModel.openSettings()
                }
                
                // Separator
                Rectangle {
                    width: 1
                    height: parent.height * 0.6
                    color: Theme.colors.border
                }
                
                // Group 5: Customize
                ToolButton {
                    width: Theme.sizing.quickAccessButtonSize
                    height: Theme.sizing.quickAccessButtonSize
                    text: "🔧"
                    ToolTip.text: "Customize Quick Access"
                    onClicked: customizeMenu.open()
                    
                    Menu {
                        id: customizeMenu
                        
                        MenuItem {
                            text: "Customize Quick Access Toolbar"
                            onTriggered: MainViewModel.customizeQuickAccess()
                        }
                        
                        MenuItem {
                            text: "Customize Ribbon"
                            onTriggered: MainViewModel.customizeRibbon()
                        }
                    }
                }
                
                // Spacer
                Item {
                    Layout.fillWidth: true
                }
            }
        }
        
        // Ribbon
        RibbonView {
            id: ribbon
            Layout.fillWidth: true
            Layout.preferredHeight: Theme.sizing.ribbonHeight
        }
        
        // Desktop Tabs
        Rectangle {
            id: desktopTabs
            Layout.fillWidth: true
            Layout.preferredHeight: 32
            color: Theme.colors.surfaceDark
            border.color: Theme.colors.border
            border.width: 1
            
            TabBar {
                id: tabBar
                anchors.fill: parent
                
                TabButton {
                    text: "Desktop 1"
                    
                    MouseArea {
                        anchors.fill: parent
                        acceptedButtons: Qt.RightButton
                        onClicked: tabContextMenu.popup()
                    }
                }
                
                Menu {
                    id: tabContextMenu
                    
                    MenuItem {
                        text: "New Tab"
                        onTriggered: MainViewModel.createNewTab()
                    }
                    
                    MenuItem {
                        text: "Close Tab"
                        onTriggered: MainViewModel.closeCurrentTab()
                    }
                    
                    MenuItem {
                        text: "Rename Tab"
                        onTriggered: MainViewModel.renameCurrentTab()
                    }
                }
            }
        }
        
        // Main Content Area (Dock Area)
        Rectangle {
            id: dockArea
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: Theme.colors.background
            
            Text {
                anchors.centerIn: parent
                text: "Dock Area - Tool windows will appear here"
                color: Theme.colors.textSecondary
                font.family: Theme.typography.fontFamily
                font.pixelSize: Theme.typography.fontSizeLarge
            }
        }
        
        // Status Bar
        Rectangle {
            id: statusBar
            Layout.fillWidth: true
            Layout.preferredHeight: Theme.sizing.statusBarHeight
            color: Theme.colors.statusBarBackground
            border.color: Theme.colors.border
            border.width: 1
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: Theme.spacing.sm
                
                // Left: App state
                Text {
                    text: "Ready"
                    color: Theme.colors.statusBarText
                    font.family: Theme.typography.fontFamily
                    font.pixelSize: Theme.typography.fontSizeSmall
                }
                
                // Spacer
                Item {
                    Layout.fillWidth: true
                }
                
                // Center: Progress
                ProgressBar {
                    Layout.preferredWidth: 200
                    value: 0.0
                    visible: false
                }
                
                // Spacer
                Item {
                    Layout.fillWidth: true
                }
                
                // Right: CAN bitrate and clock
                Row {
                    spacing: Theme.spacing.lg
                    
                    Text {
                        text: "CAN: 500 kbit/s"
                        color: Theme.colors.statusBarText
                        font.family: Theme.typography.fontFamily
                        font.pixelSize: Theme.typography.fontSizeSmall
                    }
                    
                    Text {
                        id: clockText
                        color: Theme.colors.statusBarText
                        font.family: Theme.typography.fontFamily
                        font.pixelSize: Theme.typography.fontSizeSmall
                        
                        Timer {
                            interval: 1000
                            running: true
                            repeat: true
                            onTriggered: {
                                clockText.text = Qt.formatDateTime(new Date(), "hh:mm:ss")
                            }
                        }
                        
                        Component.onCompleted: {
                            text = Qt.formatDateTime(new Date(), "hh:mm:ss")
                        }
                    }
                }
            }
        }
    }
}

# CANoeLite

A simplified Vector CANoe-style desktop application built with Qt 6 and QML, designed for Windows 11 using the MSYS2 UCRT64 toolchain.

## Features

- **Modern Ribbon Interface**: Collapsible ribbon with tabs for File, Home, Analysis, Simulation, Test, Diagnostics, Environment, Hardware, Tools, and Layout
- **Quick Access Toolbar**: Customizable toolbar with window controls, simulation controls, configuration management, and settings
- **Desktop Tabs**: Multiple desktop workspaces with right-click context menu for tab management
- **Dock System**: Resizable, floatable, and closable tool windows with layout persistence
- **Status Bar**: Real-time display of application state, progress, CAN bus bitrate, and system clock
- **MVVM Architecture**: Clean separation between UI (QML) and business logic (C++)
- **Theme System**: Bordeaux red and light gray color palette with centralized styling

## Architecture

The application follows MVVM (Model-View-ViewModel) pattern with SOLID principles:

```
CANoeLite/
├── src/
│   ├── Core/              # Core application logic
│   ├── ViewModels/        # C++ ViewModels exposed to QML
│   ├── Views/QML/         # QML user interface files
│   │   ├── Ribbon/        # Ribbon interface components
│   │   └── Tools/         # Tool window components
│   ├── Services/          # Business logic services
│   └── Resources/         # Icons and other resources
├── tests/                 # Unit tests
├── 3rdparty/             # Third-party dependencies
├── CMakeLists.txt        # Main CMake configuration
├── build.sh              # Build script for MSYS2
└── README.md             # This file
```

## Prerequisites

### MSYS2 UCRT64 Environment

1. **Install MSYS2**: Download and install from [https://www.msys2.org/](https://www.msys2.org/)

2. **Open MSYS2 UCRT64 terminal** (not MSYS2, MINGW32, or MINGW64)

3. **Update MSYS2**:
   ```bash
   pacman -Syu
   ```

4. **Install required packages**:
   ```bash
   # Build tools
   pacman -S mingw-w64-ucrt-x86_64-cmake
   pacman -S mingw-w64-ucrt-x86_64-ninja
   pacman -S mingw-w64-ucrt-x86_64-gcc
   
   # Qt 6 packages
   pacman -S mingw-w64-ucrt-x86_64-qt6-base
   pacman -S mingw-w64-ucrt-x86_64-qt6-declarative
   pacman -S mingw-w64-ucrt-x86_64-qt6-tools
   
   # Optional: Additional Qt modules
   pacman -S mingw-w64-ucrt-x86_64-qt6-svg
   pacman -S mingw-w64-ucrt-x86_64-qt6-imageformats
   ```

## Building

### Quick Build

1. **Clone or extract the project**:
   ```bash
   cd /path/to/CANoeLite
   ```

2. **Run the build script**:
   ```bash
   # Debug build (default)
   ./build.sh
   
   # Release build
   ./build.sh Release
   
   # Clean build
   ./build.sh Debug clean
   ```

### Manual Build

If you prefer to build manually:

```bash
# Create build directory
mkdir build && cd build

# Configure with CMake
cmake .. -G "Ninja" -DCMAKE_BUILD_TYPE=Release

# Build
ninja

# Run tests
ctest

# Install
ninja install
```

## Running

After building, you can run the application:

```bash
# Debug build
cd build/bin && ./CANoeLite.exe

# Release build (after install)
cd install/bin && ./CANoeLite.exe
```

## Usage

### Quick Access Toolbar

The Quick Access Toolbar provides immediate access to common functions:

- **Group 1**: Window controls (Restore, Minimize, Maximize, Close)
- **Group 2**: Simulation controls (Start, Stop)
- **Group 3**: Configuration management (Load, Save, Save As)
- **Group 4**: Settings access
- **Group 5**: Customization options

### Ribbon Interface

The ribbon is organized into functional tabs:

- **File**: Configuration management
- **Home**: Basic simulation and view controls
- **Analysis**: Trace view, statistics, and logging tools
- **Simulation**: Advanced simulation controls
- **Test**: Test execution and reporting
- **Diagnostics**: UDS and OBD tools
- **Environment**: Variable management
- **Hardware**: CAN configuration
- **Tools**: Application options
- **Layout**: Workspace layout management

### Tool Windows

Tool windows can be:
- **Opened**: Click ribbon buttons to launch tools
- **Docked**: Drag to dock areas within the main window
- **Floated**: Drag outside the main window or use the anchor button
- **Maximized**: Double-click title bar or use maximize button
- **Closed**: Click the X button in the title bar

### Desktop Tabs

- **Create**: Right-click on tab bar and select "New Tab"
- **Switch**: Click on tab to switch workspace
- **Rename**: Right-click on tab and select "Rename Tab"
- **Close**: Right-click on tab and select "Close Tab"

## Development

### Code Style

- **C++20** standard with snake_case variables and PascalCase types
- **120-column** line limit
- **QML** with camelCase IDs and PascalCase types
- **Doxygen** comments for all headers

### Adding New Tools

1. Create QML file in `src/Views/QML/Tools/`
2. Register tool in `DockManager::initializeDefaultTools()`
3. Add ribbon button to appropriate tab
4. Connect button to `RibbonViewModel::openToolWindow()`

### Testing

Run tests with:
```bash
cd build
ctest --output-on-failure
```

## Troubleshooting

### Common Issues

1. **CMake can't find Qt6**:
   - Ensure you're in MSYS2 UCRT64 environment
   - Verify Qt6 packages are installed: `pacman -Qs qt6`

2. **Build fails with missing headers**:
   - Update MSYS2: `pacman -Syu`
   - Reinstall development packages

3. **Application doesn't start**:
   - Check that all Qt6 DLLs are in PATH
   - Run from MSYS2 UCRT64 terminal

4. **QML import errors**:
   - Verify QML files are properly embedded in resources
   - Check CMake QML module configuration

### Getting Help

- Check the build output for specific error messages
- Ensure all prerequisites are correctly installed
- Verify you're using the MSYS2 UCRT64 environment

## License

This project is provided as a demonstration of Qt 6 QML application architecture and MSYS2 build processes.

## Contributing

When contributing:
1. Follow the established code style
2. Add tests for new functionality
3. Update documentation as needed
4. Ensure builds pass on MSYS2 UCRT64

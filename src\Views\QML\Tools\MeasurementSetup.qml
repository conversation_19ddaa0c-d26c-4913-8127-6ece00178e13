/**
 * @file MeasurementSetup.qml
 * @brief Measurement Setup tool window for CANoeLite
 * <AUTHOR> Team
 * @date 2025
 */

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: measurementSetup

    property string toolName: "MeasurementSetup"
    property string toolTitle: "Measurement Setup"
    property bool isFloating: false
    property bool isMaximized: false

    // State properties
    property bool isOnlineMode: true
    property bool isConnected: false

    // Theme colors
    readonly property color primaryColor: "#7A1E2A"        // Bordeaux red
    readonly property color primaryLight: "#A52A3A"        // Lighter bordeaux
    readonly property color surfaceColor: "#F2F2F2"        // Light gray
    readonly property color surfaceLight: "#FFFFFF"        // White
    readonly property color surfaceDark: "#E0E0E0"         // Darker gray
    readonly property color borderColor: "#CCCCCC"         // Border gray
    readonly property color textPrimary: "#212121"         // Dark text
    readonly property color textSecondary: "#757575"       // Medium text
    readonly property color textOnPrimary: "#FFFFFF"       // White text
    readonly property color connectedColor: "#4CAF50"      // Green for connected
    readonly property color disconnectedColor: "#F44336"   // Red for disconnected
    readonly property color blueIndicator: "#2196F3"       // Blue for connection node
    readonly property color measurementItemColor: "#B0BEC5" // Light blue-gray for measurement items
    
    color: surfaceLight
    border.color: borderColor
    border.width: 1

    RowLayout {
        anchors.fill: parent
        anchors.margins: 8
        spacing: 16

        // Left Side - Control Panel (matching reference image)
        Rectangle {
            Layout.preferredWidth: 250
            Layout.fillHeight: true
            color: surfaceLight
            border.color: borderColor
            border.width: 1
            radius: 4

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 16
                spacing: 20

                // Drive Section (top left in reference)
                Column {
                    spacing: 8

                    Text {
                        text: "Drive"
                        font.pixelSize: 12
                        font.weight: Font.Bold
                        color: textPrimary
                    }

                    Rectangle {
                        width: 80
                        height: 60
                        color: surfaceDark
                        border.color: borderColor
                        border.width: 2
                        radius: 6

                        Text {
                            anchors.centerIn: parent
                            text: "🚗"
                            font.pixelSize: 24
                            color: textSecondary
                        }
                    }
                }

                // Mode Toggle Section (center left in reference)
                Column {
                    spacing: 12

                    // Online/Offline Toggle Container
                    Rectangle {
                        width: 140
                        height: 100
                        color: surfaceColor
                        border.color: borderColor
                        border.width: 2
                        radius: 8

                        ColumnLayout {
                            anchors.fill: parent
                            anchors.margins: 12
                            spacing: 8

                            // Mode Toggle Button
                            Button {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 35
                                text: isOnlineMode ? "Online" : "Offline"

                                background: Rectangle {
                                    color: isOnlineMode ? connectedColor : primaryColor
                                    border.color: borderColor
                                    border.width: 1
                                    radius: 4
                                }

                                contentItem: Text {
                                    text: parent.text
                                    color: textOnPrimary
                                    font.pixelSize: 11
                                    font.weight: Font.Bold
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }

                                onClicked: {
                                    isOnlineMode = !isOnlineMode
                                    console.log("Mode switched to:", isOnlineMode ? "Online" : "Offline")
                                }
                            }

                            // Mode-specific Action Button
                            Button {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 35
                                text: isOnlineMode ? "Real" : "Drive"
                                enabled: true

                                background: Rectangle {
                                    color: parent.enabled ? (parent.hovered ? primaryLight : surfaceLight) : surfaceColor
                                    border.color: borderColor
                                    border.width: 1
                                    radius: 4
                                }

                                contentItem: Text {
                                    text: parent.text
                                    color: parent.enabled ? textPrimary : textSecondary
                                    font.pixelSize: 11
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }

                                onClicked: {
                                    if (isOnlineMode) {
                                        canoeOptionsDialog.open()
                                    } else {
                                        offlineModeDialog.open()
                                    }
                                }
                            }
                        }
                    }

                    // Real/Drive Button (bottom left in reference)
                    Button {
                        width: 60
                        height: 40
                        text: ">>"

                        background: Rectangle {
                            color: parent.hovered ? primaryLight : surfaceColor
                            border.color: borderColor
                            border.width: 2
                            radius: 4
                        }

                        contentItem: Text {
                            text: parent.text
                            color: textPrimary
                            font.pixelSize: 14
                            font.weight: Font.Bold
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                        }

                        onClicked: {
                            console.log("Action button clicked")
                        }
                    }
                }

                Item {
                    Layout.fillHeight: true
                }
            }
        }

        // Connection Lines and Blue Node (center area)
        Rectangle {
            Layout.preferredWidth: 100
            Layout.fillHeight: true
            color: "transparent"

            Canvas {
                id: connectionCanvas
                anchors.fill: parent

                onPaint: {
                    var ctx = getContext("2d")
                    ctx.clearRect(0, 0, width, height)

                    // Draw main vertical line
                    ctx.strokeStyle = textPrimary
                    ctx.lineWidth = 2
                    ctx.beginPath()
                    ctx.moveTo(20, 50)
                    ctx.lineTo(20, height - 50)
                    ctx.stroke()

                    // Draw horizontal connection lines to measurement items
                    var itemPositions = [80, 140, 200, 260, 320, 380]
                    for (var i = 0; i < itemPositions.length; i++) {
                        ctx.beginPath()
                        ctx.moveTo(20, itemPositions[i])
                        ctx.lineTo(60, itemPositions[i])
                        ctx.stroke()

                        // Draw small connector rectangles
                        ctx.fillStyle = borderColor
                        ctx.fillRect(55, itemPositions[i] - 3, 12, 6)
                    }
                }
            }

            // Blue Connection State Indicator (clickable node)
            Button {
                x: 10
                y: 200
                width: 20
                height: 20

                background: Rectangle {
                    color: isConnected ? blueIndicator : disconnectedColor
                    border.color: borderColor
                    border.width: 1
                    radius: 3
                }

                onClicked: {
                    isConnected = !isConnected
                    console.log("Connection state:", isConnected ? "Connected" : "Disconnected")
                }
            }
        }

        // Right Side - Measurement Tree (matching reference image)
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: surfaceLight
            border.color: borderColor
            border.width: 1
            radius: 4

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 16
                spacing: 12

                Text {
                    text: "Measurement Configuration"
                    font.pixelSize: 14
                    font.weight: Font.Bold
                    color: textPrimary
                }

                ScrollView {
                    Layout.fillWidth: true
                    Layout.fillHeight: true

                    Column {
                        width: parent.width
                        spacing: 12

                        // Measurement Items matching reference image
                        Repeater {
                            model: [
                                {name: "CAN Statistics", icon: "📊"},
                                {name: "Trace", icon: "📈"},
                                {name: "Trace 2", icon: "📈"},
                                {name: "Data", icon: "📋"},
                                {name: "Graphics", icon: "📊"},
                                {name: "Logging 2", icon: "📝"}
                            ]

                            Rectangle {
                                width: parent.width - 20
                                height: 60
                                color: measurementItemColor
                                border.color: borderColor
                                border.width: 1
                                radius: 6

                                RowLayout {
                                    anchors.fill: parent
                                    anchors.margins: 12
                                    spacing: 12

                                    Text {
                                        text: modelData.name
                                        font.pixelSize: 12
                                        font.weight: Font.Bold
                                        color: textPrimary
                                        Layout.fillWidth: true
                                    }

                                    Row {
                                        spacing: 8

                                        Rectangle {
                                            width: 24
                                            height: 24
                                            color: blueIndicator
                                            border.color: borderColor
                                            border.width: 1
                                            radius: 3

                                            Text {
                                                anchors.centerIn: parent
                                                text: modelData.icon
                                                font.pixelSize: 12
                                                color: "white"
                                            }
                                        }

                                        Rectangle {
                                            width: 24
                                            height: 24
                                            color: surfaceColor
                                            border.color: borderColor
                                            border.width: 1
                                            radius: 3

                                            Text {
                                                anchors.centerIn: parent
                                                text: "📄"
                                                font.pixelSize: 12
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        // Special Logging 2 item (bottom with file reference)
                        Rectangle {
                            width: parent.width - 20
                            height: 80
                            color: surfaceLight
                            border.color: borderColor
                            border.width: 2
                            radius: 6

                            ColumnLayout {
                                anchors.fill: parent
                                anchors.margins: 12
                                spacing: 8

                                RowLayout {
                                    Layout.fillWidth: true

                                    Text {
                                        text: "Logging 2"
                                        font.pixelSize: 12
                                        font.weight: Font.Bold
                                        color: textPrimary
                                        Layout.fillWidth: true
                                    }

                                    Row {
                                        spacing: 8

                                        Button {
                                            width: 20
                                            height: 20
                                            text: "⏸"

                                            background: Rectangle {
                                                color: surfaceColor
                                                border.color: borderColor
                                                border.width: 1
                                                radius: 3
                                            }
                                        }

                                        Button {
                                            width: 20
                                            height: 20
                                            text: "⏹"

                                            background: Rectangle {
                                                color: surfaceColor
                                                border.color: borderColor
                                                border.width: 1
                                                radius: 3
                                            }
                                        }
                                    }
                                }

                                RowLayout {
                                    Layout.fillWidth: true

                                    Text {
                                        text: "📁"
                                        font.pixelSize: 16
                                    }

                                    Text {
                                        text: "Log_662"
                                        font.pixelSize: 10
                                        color: textSecondary
                                        Layout.fillWidth: true
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // CANoe Options Dialog (for Online mode)
    Dialog {
        id: canoeOptionsDialog
        title: "CANoe Options"
        width: 400
        height: 300
        anchors.centerIn: parent
        modal: true

        background: Rectangle {
            color: surfaceLight
            border.color: borderColor
            border.width: 1
            radius: 4
        }

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 16
            spacing: 16

            Text {
                text: "CANoe Real-time Options"
                font.pixelSize: 14
                font.weight: Font.Bold
                color: textPrimary
            }

            Text {
                text: "Configure real-time measurement parameters and settings."
                font.pixelSize: 10
                color: textSecondary
                wrapMode: Text.WordWrap
                Layout.fillWidth: true
            }

            GroupBox {
                title: "Real-time Settings"
                Layout.fillWidth: true
                Layout.fillHeight: true

                ColumnLayout {
                    anchors.fill: parent
                    spacing: 8

                    CheckBox {
                        text: "Enable real-time processing"
                        checked: true
                    }

                    CheckBox {
                        text: "Auto-start measurement"
                        checked: false
                    }

                    CheckBox {
                        text: "High-precision timestamps"
                        checked: true
                    }

                    Item {
                        Layout.fillHeight: true
                    }
                }
            }

            Row {
                Layout.alignment: Qt.AlignRight
                spacing: 8

                Button {
                    text: "OK"
                    onClicked: canoeOptionsDialog.close()
                }

                Button {
                    text: "Cancel"
                    onClicked: canoeOptionsDialog.close()
                }
            }
        }
    }

    // Offline Mode Dialog (for Offline mode)
    Dialog {
        id: offlineModeDialog
        title: "Offline Mode Configuration"
        width: 400
        height: 300
        anchors.centerIn: parent
        modal: true

        background: Rectangle {
            color: surfaceLight
            border.color: borderColor
            border.width: 1
            radius: 4
        }

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 16
            spacing: 16

            Text {
                text: "Offline Drive Configuration"
                font.pixelSize: 14
                font.weight: Font.Bold
                color: textPrimary
            }

            Text {
                text: "Configure offline measurement and drive simulation settings."
                font.pixelSize: 10
                color: textSecondary
                wrapMode: Text.WordWrap
                Layout.fillWidth: true
            }

            GroupBox {
                title: "Drive Settings"
                Layout.fillWidth: true
                Layout.fillHeight: true

                ColumnLayout {
                    anchors.fill: parent
                    spacing: 8

                    CheckBox {
                        text: "Enable drive simulation"
                        checked: true
                    }

                    CheckBox {
                        text: "Load previous session"
                        checked: false
                    }

                    CheckBox {
                        text: "Replay recorded data"
                        checked: true
                    }

                    Item {
                        Layout.fillHeight: true
                    }
                }
            }

            Row {
                Layout.alignment: Qt.AlignRight
                spacing: 8

                Button {
                    text: "OK"
                    onClicked: offlineModeDialog.close()
                }

                Button {
                    text: "Cancel"
                    onClicked: offlineModeDialog.close()
                }
            }
        }
    }
}

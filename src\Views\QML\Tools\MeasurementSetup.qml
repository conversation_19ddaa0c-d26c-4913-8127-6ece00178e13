/**
 * @file MeasurementSetup.qml
 * @brief Measurement Setup tool window for CANoeLite
 * <AUTHOR> Team
 * @date 2025
 */

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: measurementSetup
    
    property string toolName: "MeasurementSetup"
    property string toolTitle: "Measurement Setup"
    property bool isFloating: false
    property bool isMaximized: false
    
    // Theme colors
    readonly property color primaryColor: "#7A1E2A"
    readonly property color surfaceColor: "#F2F2F2"
    readonly property color surfaceLight: "#FFFFFF"
    readonly property color borderColor: "#CCCCCC"
    readonly property color textPrimary: "#212121"
    readonly property color textSecondary: "#757575"
    readonly property color textOnPrimary: "#FFFFFF"
    
    color: surfaceColor
    border.color: borderColor
    border.width: 1
    
    ColumnLayout {
        anchors.fill: parent
        spacing: 0
        
        // Title Bar
        Rectangle {
            id: titleBar
            Layout.fillWidth: true
            Layout.preferredHeight: 24
            color: primaryColor
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 2
                
                Text {
                    text: measurementSetup.toolTitle
                    color: textOnPrimary
                    font.pixelSize: 11
                    font.weight: Font.Medium
                    Layout.fillWidth: true
                }
                
                // Window controls
                Row {
                    spacing: 2
                    
                    ToolButton {
                        width: 16
                        height: 16
                        text: "🗗"
                        font.pixelSize: 8
                        background: Rectangle {
                            color: parent.hovered ? "#A52A3A" : "transparent"
                            radius: 2
                        }
                        onClicked: {
                            measurementSetup.isMaximized = !measurementSetup.isMaximized
                            console.log("Toggle maximize:", measurementSetup.toolName)
                        }
                    }
                    
                    ToolButton {
                        width: 16
                        height: 16
                        text: "⚓"
                        font.pixelSize: 8
                        background: Rectangle {
                            color: parent.hovered ? "#A52A3A" : "transparent"
                            radius: 2
                        }
                        onClicked: {
                            measurementSetup.isFloating = !measurementSetup.isFloating
                            console.log("Toggle floating:", measurementSetup.toolName)
                        }
                    }
                    
                    ToolButton {
                        width: 16
                        height: 16
                        text: "✕"
                        font.pixelSize: 8
                        background: Rectangle {
                            color: parent.hovered ? "#A52A3A" : "transparent"
                            radius: 2
                        }
                        onClicked: console.log("Close tool:", measurementSetup.toolName)
                    }
                }
            }
        }
        
        // Content Area
        ScrollView {
            Layout.fillWidth: true
            Layout.fillHeight: true
            
            ColumnLayout {
                width: parent.width
                spacing: 16
                anchors.margins: 16
                
                // Measurement Configuration
                GroupBox {
                    title: "Measurement Configuration"
                    Layout.fillWidth: true
                    
                    GridLayout {
                        anchors.fill: parent
                        columns: 2
                        columnSpacing: 12
                        rowSpacing: 8
                        
                        Text {
                            text: "Measurement Name:"
                            color: textPrimary
                        }
                        TextField {
                            text: "Default Measurement"
                            Layout.fillWidth: true
                            background: Rectangle {
                                color: surfaceLight
                                border.color: borderColor
                                border.width: 1
                                radius: 2
                            }
                        }
                        
                        Text {
                            text: "Sample Rate:"
                            color: textPrimary
                        }
                        ComboBox {
                            model: ["1 Hz", "10 Hz", "100 Hz", "1 kHz", "10 kHz"]
                            currentIndex: 2
                            Layout.fillWidth: true
                            background: Rectangle {
                                color: surfaceLight
                                border.color: borderColor
                                border.width: 1
                                radius: 2
                            }
                        }
                        
                        Text {
                            text: "Duration:"
                            color: textPrimary
                        }
                        SpinBox {
                            from: 1
                            to: 3600
                            value: 60
                            Layout.fillWidth: true

                            textFromValue: function(value, locale) {
                                return value + " seconds"
                            }
                            background: Rectangle {
                                color: surfaceLight
                                border.color: borderColor
                                border.width: 1
                                radius: 2
                            }
                        }
                    }
                }
                
                // Signal Selection
                GroupBox {
                    title: "Signal Selection"
                    Layout.fillWidth: true
                    Layout.preferredHeight: 200
                    
                    ColumnLayout {
                        anchors.fill: parent
                        
                        RowLayout {
                            Layout.fillWidth: true
                            
                            Button {
                                text: "Add Signal"
                                background: Rectangle {
                                    color: parent.pressed ? primaryColor : (parent.hovered ? "#A52A3A" : surfaceColor)
                                    border.color: borderColor
                                    border.width: 1
                                    radius: 3
                                }
                            }
                            
                            Button {
                                text: "Remove Signal"
                                background: Rectangle {
                                    color: parent.pressed ? primaryColor : (parent.hovered ? "#A52A3A" : surfaceColor)
                                    border.color: borderColor
                                    border.width: 1
                                    radius: 3
                                }
                            }
                            
                            Item {
                                Layout.fillWidth: true
                            }
                        }
                        
                        ListView {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            model: ListModel {
                                ListElement { name: "Engine_Speed"; unit: "rpm"; enabled: true }
                                ListElement { name: "Vehicle_Speed"; unit: "km/h"; enabled: true }
                                ListElement { name: "Throttle_Position"; unit: "%"; enabled: false }
                                ListElement { name: "Brake_Pressure"; unit: "bar"; enabled: true }
                            }
                            
                            delegate: Rectangle {
                                width: parent.width
                                height: 30
                                color: index % 2 === 0 ? surfaceLight : surfaceColor
                                border.color: borderColor
                                border.width: 1
                                
                                RowLayout {
                                    anchors.fill: parent
                                    anchors.margins: 4
                                    
                                    CheckBox {
                                        checked: model.enabled
                                    }
                                    
                                    Text {
                                        text: model.name
                                        color: textPrimary
                                        Layout.fillWidth: true
                                    }
                                    
                                    Text {
                                        text: model.unit
                                        color: textSecondary
                                        Layout.preferredWidth: 50
                                    }
                                }
                            }
                        }
                    }
                }
                
                // Control Buttons
                RowLayout {
                    Layout.fillWidth: true
                    
                    Button {
                        text: "Start Measurement"
                        Layout.preferredWidth: 120
                        background: Rectangle {
                            color: parent.pressed ? primaryColor : (parent.hovered ? "#A52A3A" : surfaceColor)
                            border.color: borderColor
                            border.width: 1
                            radius: 3
                        }
                        onClicked: console.log("Start measurement")
                    }
                    
                    Button {
                        text: "Stop Measurement"
                        Layout.preferredWidth: 120
                        background: Rectangle {
                            color: parent.pressed ? primaryColor : (parent.hovered ? "#A52A3A" : surfaceColor)
                            border.color: borderColor
                            border.width: 1
                            radius: 3
                        }
                        onClicked: console.log("Stop measurement")
                    }
                    
                    Item {
                        Layout.fillWidth: true
                    }
                    
                    Button {
                        text: "Save Setup"
                        background: Rectangle {
                            color: parent.pressed ? primaryColor : (parent.hovered ? "#A52A3A" : surfaceColor)
                            border.color: borderColor
                            border.width: 1
                            radius: 3
                        }
                        onClicked: console.log("Save setup")
                    }
                }
            }
        }
        
        // Status Bar
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 20
            color: "#E0E0E0"
            border.color: borderColor
            border.width: 1
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 4
                
                Text {
                    text: "Ready for measurement"
                    color: textSecondary
                    font.pixelSize: 10
                }
                
                Item {
                    Layout.fillWidth: true
                }
                
                Text {
                    text: "Signals: 3/4 selected"
                    color: textSecondary
                    font.pixelSize: 10
                }
            }
        }
    }
}

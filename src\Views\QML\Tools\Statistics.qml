/**
 * @file Statistics.qml
 * @brief Statistics tool window for CANoeLite
 * <AUTHOR> Team
 * @date 2025
 */

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import CANoeLite 1.0
import ".."

Rectangle {
    id: statisticsView
    
    property string toolName: "Statistics"
    property string toolTitle: "Statistics"
    property bool isFloating: false
    property bool isMaximized: false
    
    color: Theme.colors.surface
    border.color: Theme.colors.dockBorder
    border.width: 1
    
    ColumnLayout {
        anchors.fill: parent
        spacing: 0
        
        // Title Bar
        Rectangle {
            id: titleBar
            Layout.fillWidth: true
            Layout.preferredHeight: Theme.sizing.dockTitleBarHeight
            color: Theme.colors.dockTitleBar
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: Theme.spacing.xs
                
                Text {
                    text: statisticsView.toolTitle
                    color: Theme.colors.textOnPrimary
                    font.family: Theme.typography.fontFamily
                    font.pixelSize: Theme.typography.fontSizeSmall
                    font.weight: Theme.typography.fontWeightMedium
                    Layout.fillWidth: true
                }
                
                // Window controls
                Row {
                    spacing: Theme.spacing.xs
                    
                    ToolButton {
                        width: 16
                        height: 16
                        text: "🗗"
                        font.pixelSize: 8
                        onClicked: {
                            statisticsView.isMaximized = !statisticsView.isMaximized
                            DockViewModel.setToolMaximized(statisticsView.toolName, statisticsView.isMaximized)
                        }
                    }
                    
                    ToolButton {
                        width: 16
                        height: 16
                        text: "⚓"
                        font.pixelSize: 8
                        onClicked: {
                            statisticsView.isFloating = !statisticsView.isFloating
                            DockViewModel.setToolFloating(statisticsView.toolName, statisticsView.isFloating)
                        }
                    }
                    
                    ToolButton {
                        width: 16
                        height: 16
                        text: "✕"
                        font.pixelSize: 8
                        onClicked: DockViewModel.closeTool(statisticsView.toolName)
                    }
                }
            }
            
            // Make title bar draggable
            MouseArea {
                anchors.fill: parent
                drag.target: statisticsView.isFloating ? statisticsView : null
                onDoubleClicked: {
                    statisticsView.isMaximized = !statisticsView.isMaximized
                    DockViewModel.setToolMaximized(statisticsView.toolName, statisticsView.isMaximized)
                }
            }
        }
        
        // Toolbar
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 32
            color: Theme.colors.surfaceLight
            border.color: Theme.colors.borderLight
            border.width: 1
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: Theme.spacing.sm
                
                Button {
                    text: "Reset"
                    Layout.preferredWidth: 60
                    Layout.preferredHeight: 24
                    onClicked: statisticsModel.resetStatistics()
                }
                
                Button {
                    text: "Export"
                    Layout.preferredWidth: 60
                    Layout.preferredHeight: 24
                    onClicked: statisticsModel.exportStatistics()
                }
                
                Rectangle {
                    width: 1
                    height: parent.height * 0.6
                    color: Theme.colors.border
                }
                
                CheckBox {
                    text: "Auto Update"
                    checked: true
                }
                
                Item {
                    Layout.fillWidth: true
                }
                
                Text {
                    text: "Last Update: " + statisticsModel.lastUpdate
                    color: Theme.colors.textSecondary
                    font.family: Theme.typography.fontFamily
                    font.pixelSize: Theme.typography.fontSizeSmall
                }
            }
        }
        
        // Content Area
        ScrollView {
            Layout.fillWidth: true
            Layout.fillHeight: true
            
            GridLayout {
                columns: 2
                columnSpacing: Theme.spacing.lg
                rowSpacing: Theme.spacing.md
                anchors.margins: Theme.spacing.lg
                
                // CAN Statistics
                GroupBox {
                    title: "CAN Statistics"
                    Layout.fillWidth: true
                    Layout.preferredHeight: 200
                    
                    GridLayout {
                        anchors.fill: parent
                        columns: 2
                        
                        Text {
                            text: "Total Messages:"
                            font.family: Theme.typography.fontFamily
                            font.pixelSize: Theme.typography.fontSizeMedium
                        }
                        Text {
                            text: statisticsModel.totalMessages
                            font.family: Theme.typography.monoFontFamily
                            font.pixelSize: Theme.typography.fontSizeMedium
                            color: Theme.colors.primary
                        }
                        
                        Text {
                            text: "Messages/sec:"
                            font.family: Theme.typography.fontFamily
                            font.pixelSize: Theme.typography.fontSizeMedium
                        }
                        Text {
                            text: statisticsModel.messagesPerSecond
                            font.family: Theme.typography.monoFontFamily
                            font.pixelSize: Theme.typography.fontSizeMedium
                            color: Theme.colors.info
                        }
                        
                        Text {
                            text: "Error Frames:"
                            font.family: Theme.typography.fontFamily
                            font.pixelSize: Theme.typography.fontSizeMedium
                        }
                        Text {
                            text: statisticsModel.errorFrames
                            font.family: Theme.typography.monoFontFamily
                            font.pixelSize: Theme.typography.fontSizeMedium
                            color: statisticsModel.errorFrames > 0 ? Theme.colors.error : Theme.colors.success
                        }
                        
                        Text {
                            text: "Bus Load:"
                            font.family: Theme.typography.fontFamily
                            font.pixelSize: Theme.typography.fontSizeMedium
                        }
                        Text {
                            text: statisticsModel.busLoad + "%"
                            font.family: Theme.typography.monoFontFamily
                            font.pixelSize: Theme.typography.fontSizeMedium
                            color: statisticsModel.busLoad > 80 ? Theme.colors.warning : Theme.colors.success
                        }
                    }
                }
                
                // Network Statistics
                GroupBox {
                    title: "Network Statistics"
                    Layout.fillWidth: true
                    Layout.preferredHeight: 200
                    
                    GridLayout {
                        anchors.fill: parent
                        columns: 2
                        
                        Text {
                            text: "Active Nodes:"
                            font.family: Theme.typography.fontFamily
                            font.pixelSize: Theme.typography.fontSizeMedium
                        }
                        Text {
                            text: statisticsModel.activeNodes
                            font.family: Theme.typography.monoFontFamily
                            font.pixelSize: Theme.typography.fontSizeMedium
                            color: Theme.colors.primary
                        }
                        
                        Text {
                            text: "Unique IDs:"
                            font.family: Theme.typography.fontFamily
                            font.pixelSize: Theme.typography.fontSizeMedium
                        }
                        Text {
                            text: statisticsModel.uniqueIds
                            font.family: Theme.typography.monoFontFamily
                            font.pixelSize: Theme.typography.fontSizeMedium
                            color: Theme.colors.info
                        }
                        
                        Text {
                            text: "Data Bytes:"
                            font.family: Theme.typography.fontFamily
                            font.pixelSize: Theme.typography.fontSizeMedium
                        }
                        Text {
                            text: statisticsModel.dataBytes
                            font.family: Theme.typography.monoFontFamily
                            font.pixelSize: Theme.typography.fontSizeMedium
                            color: Theme.colors.textPrimary
                        }
                        
                        Text {
                            text: "Throughput:"
                            font.family: Theme.typography.fontFamily
                            font.pixelSize: Theme.typography.fontSizeMedium
                        }
                        Text {
                            text: statisticsModel.throughput + " kB/s"
                            font.family: Theme.typography.monoFontFamily
                            font.pixelSize: Theme.typography.fontSizeMedium
                            color: Theme.colors.success
                        }
                    }
                }
            }
        }
        
        // Status Bar
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 20
            color: Theme.colors.statusBarBackground
            border.color: Theme.colors.border
            border.width: 1
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: Theme.spacing.xs
                
                Text {
                    text: statisticsModel.isCollecting ? "Collecting..." : "Stopped"
                    color: statisticsModel.isCollecting ? Theme.colors.success : Theme.colors.textSecondary
                    font.family: Theme.typography.fontFamily
                    font.pixelSize: Theme.typography.fontSizeXSmall
                }
                
                Item {
                    Layout.fillWidth: true
                }
                
                Text {
                    text: "Uptime: " + statisticsModel.uptime
                    color: Theme.colors.textSecondary
                    font.family: Theme.typography.fontFamily
                    font.pixelSize: Theme.typography.fontSizeXSmall
                }
            }
        }
    }
    
    // Sample statistics model
    QtObject {
        id: statisticsModel
        
        property bool isCollecting: true
        property string lastUpdate: "12:34:56"
        property int totalMessages: 15847
        property int messagesPerSecond: 125
        property int errorFrames: 0
        property real busLoad: 23.5
        property int activeNodes: 8
        property int uniqueIds: 42
        property string dataBytes: "126,376"
        property real throughput: 15.2
        property string uptime: "02:15:33"
        
        function resetStatistics() {
            totalMessages = 0
            errorFrames = 0
            console.log("Statistics: Reset statistics")
        }
        
        function exportStatistics() {
            console.log("Statistics: Export statistics")
        }
    }
}

/**
 * @file RibbonViewModel.h
 * @brief View model for ribbon interface operations
 * <AUTHOR> Team
 * @date 2025
 */

#pragma once

#include <QObject>

namespace ViewModels {

/**
 * @brief View model that handles ribbon button operations
 */
class RibbonViewModel : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Constructor
     * @param parent Parent object
     */
    explicit RibbonViewModel(QObject* parent = nullptr);

public slots:
    // File operations
    void newConfiguration();
    void openConfiguration();
    void saveConfiguration();
    
    // Simulation operations
    void startSimulation();
    void stopSimulation();
    void pauseSimulation();
    void resetSimulation();
    void stepSimulation();
    
    // View operations
    void zoomIn();
    void zoomOut();
    void fitAll();
    
    // Analysis operations
    void openTraceView();
    void openStatistics();
    void startLogging();
    void stopLogging();
    
    // Test operations
    void runTest();
    void openTestReport();
    
    // Diagnostics operations
    void openUDS();
    void openOBD();
    
    // Environment operations
    void openVariables();
    
    // Hardware operations
    void openCANConfig();
    
    // Tools operations
    void openOptions();
    
    // Layout operations
    void saveLayout();
    void loadLayout();

signals:
    /**
     * @brief Emitted when a tool window should be opened
     * @param toolName Name of the tool to open
     */
    void openToolWindow(const QString& toolName);
};

} // namespace ViewModels

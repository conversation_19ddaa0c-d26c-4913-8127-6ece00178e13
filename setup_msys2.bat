@echo off
REM CANoeLite MSYS2 Setup Script
REM This script helps set up the MSYS2 environment for building CANoeLite

echo ========================================
echo CANoeLite MSYS2 Setup Helper
echo ========================================
echo.

echo This script will help you set up MSYS2 for building CANoeLite.
echo.

echo Step 1: Download and Install MSYS2
echo -----------------------------------
echo 1. Go to https://www.msys2.org/
echo 2. Download the installer for your system
echo 3. Run the installer and follow the instructions
echo 4. After installation, open "MSYS2 UCRT64" terminal
echo.

echo Step 2: Update MSYS2
echo --------------------
echo Run this command in MSYS2 UCRT64 terminal:
echo   pacman -Syu
echo.
echo If prompted to close the terminal, do so and reopen it, then run:
echo   pacman -Su
echo.

echo Step 3: Install Build Tools
echo ---------------------------
echo Run these commands in MSYS2 UCRT64 terminal:
echo.
echo # Build tools
echo pacman -S mingw-w64-ucrt-x86_64-cmake
echo pacman -S mingw-w64-ucrt-x86_64-ninja
echo pacman -S mingw-w64-ucrt-x86_64-gcc
echo.
echo # Qt 6 packages
echo pacman -S mingw-w64-ucrt-x86_64-qt6-base
echo pacman -S mingw-w64-ucrt-x86_64-qt6-declarative
echo pacman -S mingw-w64-ucrt-x86_64-qt6-tools
echo.

echo Step 4: Build CANoeLite
echo -----------------------
echo After installing the packages, navigate to the CANoeLite directory
echo in the MSYS2 UCRT64 terminal and run:
echo   ./build.sh
echo.

echo Step 5: Alternative - Install All at Once
echo -----------------------------------------
echo You can also install all packages at once with this command:
echo.
echo pacman -S mingw-w64-ucrt-x86_64-cmake mingw-w64-ucrt-x86_64-ninja mingw-w64-ucrt-x86_64-gcc mingw-w64-ucrt-x86_64-qt6-base mingw-w64-ucrt-x86_64-qt6-declarative mingw-w64-ucrt-x86_64-qt6-tools
echo.

echo ========================================
echo Setup complete! Follow the steps above.
echo ========================================
pause

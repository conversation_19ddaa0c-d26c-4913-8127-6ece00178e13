/**
 * @file DockManager.cpp
 * @brief Implementation of DockManager service
 * <AUTHOR> Team
 * @date 2025
 */

#include "DockManager.h"
#include <QDebug>

namespace Services {

DockManager::DockManager(QObject* parent)
    : QObject(parent)
    , m_settings(new QSettings(this))
    , m_currentLayout("default")
{
    initializeDefaultTools();
    loadLayoutSettings();
}

DockManager::~DockManager()
{
    saveLayoutSettings();
}

bool DockManager::openTool(const QString& toolName)
{
    if (m_openTools.contains(toolName)) {
        qDebug() << "Tool already open:" << toolName;
        return true;
    }
    
    ToolInfo* toolInfo = getToolInfo(toolName);
    if (!toolInfo) {
        qWarning() << "Unknown tool:" << toolName;
        return false;
    }
    
    m_openTools.append(toolName);
    toolInfo->isVisible = true;
    
    // Create tool info map for QML
    QVariantMap toolInfoMap;
    toolInfoMap["name"] = toolInfo->name;
    toolInfoMap["title"] = toolInfo->title;
    toolInfoMap["qmlSource"] = toolInfo->qmlSource;
    toolInfoMap["geometry"] = toolInfo->geometry;
    toolInfoMap["isFloating"] = toolInfo->isFloating;
    toolInfoMap["isVisible"] = toolInfo->isVisible;
    toolInfoMap["isMaximized"] = toolInfo->isMaximized;
    
    emit openToolsChanged();
    emit toolOpened(toolName, toolInfoMap);
    
    qDebug() << "Opened tool:" << toolName;
    return true;
}

void DockManager::closeTool(const QString& toolName)
{
    if (!m_openTools.contains(toolName)) {
        return;
    }
    
    m_openTools.removeAll(toolName);
    
    ToolInfo* toolInfo = getToolInfo(toolName);
    if (toolInfo) {
        toolInfo->isVisible = false;
    }
    
    emit openToolsChanged();
    emit toolClosed(toolName);
    
    qDebug() << "Closed tool:" << toolName;
}

void DockManager::toggleTool(const QString& toolName)
{
    if (m_openTools.contains(toolName)) {
        closeTool(toolName);
    } else {
        openTool(toolName);
    }
}

void DockManager::setToolGeometry(const QString& toolName, const QVariantMap& geometry)
{
    ToolInfo* toolInfo = getToolInfo(toolName);
    if (!toolInfo) {
        return;
    }
    
    toolInfo->geometry = geometry;
    emit toolGeometryChanged(toolName, geometry);
    
    qDebug() << "Updated geometry for tool:" << toolName;
}

void DockManager::setToolFloating(const QString& toolName, bool floating)
{
    ToolInfo* toolInfo = getToolInfo(toolName);
    if (!toolInfo) {
        return;
    }
    
    toolInfo->isFloating = floating;
    qDebug() << "Set floating state for tool:" << toolName << "to" << floating;
}

void DockManager::setToolMaximized(const QString& toolName, bool maximized)
{
    ToolInfo* toolInfo = getToolInfo(toolName);
    if (!toolInfo) {
        return;
    }
    
    toolInfo->isMaximized = maximized;
    qDebug() << "Set maximized state for tool:" << toolName << "to" << maximized;
}

void DockManager::saveLayout(const QString& layoutName)
{
    m_settings->beginGroup("layouts/" + layoutName);
    
    // Save open tools
    m_settings->setValue("openTools", m_openTools);
    
    // Save tool configurations
    for (auto it = m_toolInfos.begin(); it != m_toolInfos.end(); ++it) {
        const QString& toolName = it.key();
        const ToolInfo& toolInfo = it.value();
        
        m_settings->beginGroup(toolName);
        m_settings->setValue("geometry", toolInfo.geometry);
        m_settings->setValue("isFloating", toolInfo.isFloating);
        m_settings->setValue("isVisible", toolInfo.isVisible);
        m_settings->setValue("isMaximized", toolInfo.isMaximized);
        m_settings->endGroup();
    }
    
    m_settings->endGroup();
    m_settings->sync();
    
    qDebug() << "Saved layout:" << layoutName;
}

void DockManager::loadLayout(const QString& layoutName)
{
    m_settings->beginGroup("layouts/" + layoutName);
    
    // Close all current tools
    QStringList currentTools = m_openTools;
    for (const QString& toolName : currentTools) {
        closeTool(toolName);
    }
    
    // Load tool configurations
    for (auto it = m_toolInfos.begin(); it != m_toolInfos.end(); ++it) {
        const QString& toolName = it.key();
        ToolInfo& toolInfo = it.value();
        
        m_settings->beginGroup(toolName);
        toolInfo.geometry = m_settings->value("geometry", QVariantMap()).toMap();
        toolInfo.isFloating = m_settings->value("isFloating", false).toBool();
        toolInfo.isVisible = m_settings->value("isVisible", false).toBool();
        toolInfo.isMaximized = m_settings->value("isMaximized", false).toBool();
        m_settings->endGroup();
        
        // Open tool if it was visible
        if (toolInfo.isVisible) {
            openTool(toolName);
        }
    }
    
    m_settings->endGroup();
    m_currentLayout = layoutName;
    
    qDebug() << "Loaded layout:" << layoutName;
}

QStringList DockManager::getAvailableLayouts() const
{
    m_settings->beginGroup("layouts");
    QStringList layouts = m_settings->childGroups();
    m_settings->endGroup();
    
    if (layouts.isEmpty()) {
        layouts.append("default");
    }
    
    return layouts;
}

void DockManager::deleteLayout(const QString& layoutName)
{
    if (layoutName == "default") {
        qWarning() << "Cannot delete default layout";
        return;
    }
    
    m_settings->beginGroup("layouts");
    m_settings->remove(layoutName);
    m_settings->endGroup();
    m_settings->sync();
    
    qDebug() << "Deleted layout:" << layoutName;
}

void DockManager::initializeDefaultTools()
{
    // TraceView tool
    ToolInfo traceView;
    traceView.name = "TraceView";
    traceView.title = "Trace View";
    traceView.qmlSource = "qrc:/CANoeLite/Views/QML/Tools/TraceView.qml";
    traceView.geometry = QVariantMap{{"x", 100}, {"y", 100}, {"width", 600}, {"height", 400}};
    traceView.isFloating = false;
    traceView.isVisible = false;
    traceView.isMaximized = false;
    m_toolInfos["TraceView"] = traceView;
    
    // Statistics tool
    ToolInfo statistics;
    statistics.name = "Statistics";
    statistics.title = "Statistics";
    statistics.qmlSource = "qrc:/CANoeLite/Views/QML/Tools/Statistics.qml";
    statistics.geometry = QVariantMap{{"x", 150}, {"y", 150}, {"width", 500}, {"height", 300}};
    statistics.isFloating = false;
    statistics.isVisible = false;
    statistics.isMaximized = false;
    m_toolInfos["Statistics"] = statistics;
    
    // Add more default tools as needed...
    
    qDebug() << "Initialized" << m_toolInfos.size() << "default tools";
}

DockManager::ToolInfo* DockManager::getToolInfo(const QString& toolName)
{
    auto it = m_toolInfos.find(toolName);
    if (it != m_toolInfos.end()) {
        return &it.value();
    }
    return nullptr;
}

void DockManager::loadLayoutSettings()
{
    QString savedLayout = m_settings->value("currentLayout", "default").toString();
    loadLayout(savedLayout);
}

void DockManager::saveLayoutSettings()
{
    m_settings->setValue("currentLayout", m_currentLayout);
    saveLayout(m_currentLayout);
}

} // namespace Services

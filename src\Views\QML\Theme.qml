/**
 * @file Theme.qml
 * @brief Centralized theme and color constants for CANoeLite
 * <AUTHOR> Team
 * @date 2025
 */

pragma Singleton
import QtQuick 2.15

QtObject {
    id: theme
    
    // Color Constants
    readonly property QtObject colors: QtObject {
        // Primary Colors
        readonly property color primary: "#7A1E2A"          // Bordeaux red
        readonly property color primaryLight: "#A52A3A"     // Lighter bordeaux
        readonly property color primaryDark: "#5A1620"      // Darker bordeaux
        
        // Surface Colors
        readonly property color surface: "#F2F2F2"          // Light gray
        readonly property color surfaceLight: "#FFFFFF"     // White
        readonly property color surfaceDark: "#E0E0E0"      // Darker gray
        
        // Background Colors
        readonly property color background: "#F8F8F8"       // Very light gray
        readonly property color backgroundDark: "#EEEEEE"   // Slightly darker
        
        // Text Colors
        readonly property color textPrimary: "#212121"      // Dark gray
        readonly property color textSecondary: "#757575"    // Medium gray
        readonly property color textDisabled: "#BDBDBD"     // Light gray
        readonly property color textOnPrimary: "#FFFFFF"    // White on primary
        
        // Border Colors
        readonly property color border: "#CCCCCC"           // Light border
        readonly property color borderDark: "#999999"       // Dark border
        readonly property color borderLight: "#E0E0E0"      // Very light border
        
        // State Colors
        readonly property color success: "#4CAF50"          // Green
        readonly property color warning: "#FF9800"          // Orange
        readonly property color error: "#F44336"            // Red
        readonly property color info: "#2196F3"             // Blue
        
        // Hover and Focus Colors
        readonly property color hover: "#F0F0F0"            // Light hover
        readonly property color focus: "#E3F2FD"            // Light blue focus
        readonly property color pressed: "#E0E0E0"          // Pressed state
        
        // Ribbon specific colors
        readonly property color ribbonBackground: surface
        readonly property color ribbonTabActive: primary
        readonly property color ribbonTabInactive: surfaceDark
        readonly property color ribbonPanelBackground: surfaceLight
        
        // Dock colors
        readonly property color dockBackground: surface
        readonly property color dockTitleBar: primaryLight
        readonly property color dockBorder: borderDark
        
        // Status bar colors
        readonly property color statusBarBackground: surfaceDark
        readonly property color statusBarText: textSecondary
    }
    
    // Typography
    readonly property QtObject typography: QtObject {
        // Font families
        readonly property string fontFamily: "Segoe UI"
        readonly property string monoFontFamily: "Consolas"
        
        // Font sizes
        readonly property int fontSizeXSmall: 10
        readonly property int fontSizeSmall: 11
        readonly property int fontSizeMedium: 12
        readonly property int fontSizeLarge: 14
        readonly property int fontSizeXLarge: 16
        readonly property int fontSizeXXLarge: 18
        
        // Font weights
        readonly property int fontWeightLight: Font.Light
        readonly property int fontWeightNormal: Font.Normal
        readonly property int fontWeightMedium: Font.Medium
        readonly property int fontWeightBold: Font.Bold
    }
    
    // Spacing and Sizing
    readonly property QtObject spacing: QtObject {
        readonly property int xs: 2
        readonly property int sm: 4
        readonly property int md: 8
        readonly property int lg: 12
        readonly property int xl: 16
        readonly property int xxl: 24
        readonly property int xxxl: 32
    }
    
    readonly property QtObject sizing: QtObject {
        // Button sizes
        readonly property int buttonHeight: 32
        readonly property int buttonHeightSmall: 24
        readonly property int buttonHeightLarge: 40
        
        // Ribbon sizes
        readonly property int ribbonHeight: 120
        readonly property int ribbonTabHeight: 24
        readonly property int ribbonPanelMinWidth: 80
        
        // Dock sizes
        readonly property int dockTitleBarHeight: 24
        readonly property int dockMinWidth: 200
        readonly property int dockMinHeight: 150
        
        // Status bar
        readonly property int statusBarHeight: 24
        
        // Quick access toolbar
        readonly property int quickAccessHeight: 32
        readonly property int quickAccessButtonSize: 24
    }
    
    // Border radius
    readonly property QtObject radius: QtObject {
        readonly property int none: 0
        readonly property int small: 2
        readonly property int medium: 4
        readonly property int large: 6
        readonly property int xlarge: 8
    }
    
    // Shadows
    readonly property QtObject shadows: QtObject {
        readonly property int elevation1: 1
        readonly property int elevation2: 2
        readonly property int elevation3: 4
        readonly property int elevation4: 6
        readonly property int elevation5: 8
    }
    
    // Animation durations (in milliseconds)
    readonly property QtObject animation: QtObject {
        readonly property int fast: 150
        readonly property int normal: 250
        readonly property int slow: 350
    }
}

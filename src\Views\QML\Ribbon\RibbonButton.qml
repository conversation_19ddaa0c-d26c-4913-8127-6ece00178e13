/**
 * @file RibbonButton.qml
 * @brief Standardized ribbon button component for CANoeLite
 * <AUTHOR> Team
 * @date 2025
 */

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Button {
    id: ribbonButton
    
    property string buttonText: ""
    property string iconText: "📄" // Default icon
    property color primaryColor: "#7A1E2A"
    property color primaryLight: "#A52A3A"
    property color surfaceColor: "#F2F2F2"
    property color borderColor: "#CCCCCC"
    property color textPrimary: "#212121"
    
    width: 60
    height: 60
    
    background: Rectangle {
        color: ribbonButton.pressed ? primaryColor : (ribbonButton.hovered ? primaryLight : surfaceColor)
        border.color: borderColor
        border.width: 1
        radius: 3
        
        // Subtle shadow effect
        Rectangle {
            anchors.fill: parent
            anchors.topMargin: 1
            color: "transparent"
            border.color: Qt.lighter(borderColor, 1.2)
            border.width: ribbonButton.hovered ? 1 : 0
            radius: 3
        }
    }
    
    contentItem: Column {
        anchors.centerIn: parent
        spacing: 2
        
        // Icon at top
        Text {
            id: iconLabel
            anchors.horizontalCenter: parent.horizontalCenter
            archors.verticalCenter: parent.verticalCenter
            text: ribbonButton.iconText
            font.pixelSize: 20
            color: ribbonButton.pressed ? "#FFFFFF" : textPrimary
            horizontalAlignment: Text.AlignHCenter
        }
        
        // Text label below icon
        Text {
            id: textLabel
            anchors.horizontalCenter: parent.horizontalCenter
            archors.verticalCenter: parent.verticalCenter
            text: ribbonButton.buttonText
            font.pixelSize: 8
            color: ribbonButton.pressed ? "#FFFFFF" : textPrimary
            horizontalAlignment: Text.AlignHCenter
            wrapMode: Text.WordWrap
            width: ribbonButton.width - 8
            maximumLineCount: 2
            elide: Text.ElideRight
        }
    }
    
    // Hover animation
    Behavior on background.color {
        ColorAnimation {
            duration: 150
        }
    }
}

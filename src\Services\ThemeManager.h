/**
 * @file ThemeManager.h
 * @brief Theme management service for CANoeLite
 * <AUTHOR> Team
 * @date 2025
 */

#pragma once

#include <QObject>
#include <QColor>
#include <QString>

namespace Services {

/**
 * @brief Manages application theming and color schemes
 */
class ThemeManager : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QString currentTheme READ currentTheme WRITE setCurrentTheme NOTIFY currentThemeChanged)
    Q_PROPERTY(QColor primaryColor READ primaryColor NOTIFY primaryColorChanged)
    Q_PROPERTY(QColor surfaceColor READ surfaceColor NOTIFY surfaceColorChanged)

public:
    /**
     * @brief Available theme types
     */
    enum class ThemeType {
        Light,
        Dark,
        Auto
    };
    Q_ENUM(ThemeType)

    /**
     * @brief Constructor
     * @param parent Parent object
     */
    explicit ThemeManager(QObject* parent = nullptr);
    
    /**
     * @brief Get current theme name
     * @return Current theme name
     */
    QString currentTheme() const { return m_currentTheme; }
    
    /**
     * @brief Set current theme
     * @param theme Theme name
     */
    void setCurrentTheme(const QString& theme);
    
    /**
     * @brief Get primary color
     * @return Primary color
     */
    QColor primaryColor() const { return m_primaryColor; }
    
    /**
     * @brief Get surface color
     * @return Surface color
     */
    QColor surfaceColor() const { return m_surfaceColor; }

public slots:
    /**
     * @brief Apply theme to application
     * @param themeType Theme type to apply
     */
    void applyTheme(ThemeType themeType);

signals:
    /**
     * @brief Emitted when current theme changes
     */
    void currentThemeChanged();
    
    /**
     * @brief Emitted when primary color changes
     */
    void primaryColorChanged();
    
    /**
     * @brief Emitted when surface color changes
     */
    void surfaceColorChanged();

private:
    /**
     * @brief Load theme settings
     */
    void loadThemeSettings();
    
    /**
     * @brief Save theme settings
     */
    void saveThemeSettings();

private:
    QString m_currentTheme;     ///< Current theme name
    QColor m_primaryColor;      ///< Primary color
    QColor m_surfaceColor;      ///< Surface color
    ThemeType m_themeType;      ///< Current theme type
};

} // namespace Services

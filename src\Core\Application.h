/**
 * @file Application.h
 * @brief Core application class for CANoeLite
 * <AUTHOR> Team
 * @date 2025
 */

#pragma once

#include <QObject>
#include <QString>
#include <QSettings>

namespace Core {

/**
 * @brief Core application class that manages application lifecycle and settings
 */
class Application : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Constructor
     * @param parent Parent object
     */
    explicit Application(QObject* parent = nullptr);
    
    /**
     * @brief Destructor
     */
    ~Application() override;
    
    /**
     * @brief Initialize the application
     * @return True if initialization was successful
     */
    bool initialize();
    
    /**
     * @brief Shutdown the application
     */
    void shutdown();
    
    /**
     * @brief Get application settings
     * @return Pointer to QSettings instance
     */
    QSettings* settings() const { return m_settings; }
    
    /**
     * @brief Get application data directory
     * @return Path to application data directory
     */
    QString dataDirectory() const { return m_dataDirectory; }

signals:
    /**
     * @brief Emitted when application is about to quit
     */
    void aboutToQuit();

private slots:
    /**
     * @brief Handle application quit signal
     */
    void onAboutToQuit();

private:
    /**
     * @brief Setup application directories
     */
    void setupDirectories();
    
    /**
     * @brief Load application settings
     */
    void loadSettings();

private:
    QSettings* m_settings;          ///< Application settings
    QString m_dataDirectory;        ///< Application data directory
    bool m_initialized;             ///< Initialization flag
};

} // namespace Core

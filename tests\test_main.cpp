/**
 * @file test_main.cpp
 * @brief Main test file for CANoeLite
 * <AUTHOR> Team
 * @date 2025
 */

#include <QtTest/QtTest>
#include <QCoreApplication>

/**
 * @brief Basic test class for CANoeLite
 */
class CANoeLiteTest : public QObject
{
    Q_OBJECT

private slots:
    /**
     * @brief Test basic functionality
     */
    void testBasic()
    {
        QVERIFY(true);
        QCOMPARE(1 + 1, 2);
    }
    
    /**
     * @brief Test application properties
     */
    void testApplicationProperties()
    {
        QCoreApplication::setApplicationName("CANoeLite");
        QCoreApplication::setApplicationVersion("1.0.0");
        
        QCOMPARE(QCoreApplication::applicationName(), QString("CANoeLite"));
        QCOMPARE(QCoreApplication::applicationVersion(), QString("1.0.0"));
    }
};

QTEST_MAIN(CANoeLiteTest)
#include "test_main.moc"

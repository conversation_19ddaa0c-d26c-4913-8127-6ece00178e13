#!/bin/bash

# CANoeLite Build Script for MSYS2 UCRT64
# This script builds the CANoeLite application using the MSYS2 UCRT64 toolchain

set -e  # Exit on any error

# Configuration
BUILD_TYPE="${1:-Debug}"  # Default to Debug, can be overridden with Release
BUILD_DIR="build"
INSTALL_DIR="install"
JOBS=$(nproc)  # Use all available CPU cores

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if we're in MSYS2 environment
check_msys2() {
    if [[ ! "$MSYSTEM" == "UCRT64" ]]; then
        print_error "This script must be run in MSYS2 UCRT64 environment"
        print_error "Please start MSYS2 UCRT64 terminal and run this script again"
        exit 1
    fi
    print_success "Running in MSYS2 UCRT64 environment"
}

# Function to check required dependencies
check_dependencies() {
    print_status "Checking dependencies..."
    
    local missing_deps=()
    
    # Check for required packages
    if ! command -v cmake &> /dev/null; then
        missing_deps+=("mingw-w64-ucrt-x86_64-cmake")
    fi
    
    if ! command -v ninja &> /dev/null; then
        missing_deps+=("mingw-w64-ucrt-x86_64-ninja")
    fi
    
    if ! command -v gcc &> /dev/null; then
        missing_deps+=("mingw-w64-ucrt-x86_64-gcc")
    fi
    
    # Check for Qt6
    if ! pkg-config --exists Qt6Core; then
        missing_deps+=("mingw-w64-ucrt-x86_64-qt6-base")
        missing_deps+=("mingw-w64-ucrt-x86_64-qt6-declarative")
        missing_deps+=("mingw-w64-ucrt-x86_64-qt6-tools")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_error "Missing dependencies. Please install:"
        for dep in "${missing_deps[@]}"; do
            echo "  pacman -S $dep"
        done
        exit 1
    fi
    
    print_success "All dependencies are available"
}

# Function to clean build directory
clean_build() {
    if [ -d "$BUILD_DIR" ]; then
        print_status "Cleaning existing build directory..."
        rm -rf "$BUILD_DIR"
    fi
    
    if [ -d "$INSTALL_DIR" ]; then
        print_status "Cleaning existing install directory..."
        rm -rf "$INSTALL_DIR"
    fi
}

# Function to configure the project
configure_project() {
    print_status "Configuring CANoeLite with CMake..."
    
    mkdir -p "$BUILD_DIR"
    cd "$BUILD_DIR"
    
    cmake .. \
        -G "Ninja" \
        -DCMAKE_BUILD_TYPE="$BUILD_TYPE" \
        -DCMAKE_INSTALL_PREFIX="../$INSTALL_DIR" \
        -DCMAKE_PREFIX_PATH="$MINGW_PREFIX" \
        -DQt6_DIR="$MINGW_PREFIX/lib/cmake/Qt6"
    
    cd ..
    print_success "Configuration completed"
}

# Function to build the project
build_project() {
    print_status "Building CANoeLite..."
    
    cd "$BUILD_DIR"
    ninja -j "$JOBS"
    cd ..
    
    print_success "Build completed"
}

# Function to run tests
run_tests() {
    print_status "Running tests..."
    
    cd "$BUILD_DIR"
    if ctest --output-on-failure; then
        print_success "All tests passed"
    else
        print_warning "Some tests failed"
    fi
    cd ..
}

# Function to install the project
install_project() {
    print_status "Installing CANoeLite..."
    
    cd "$BUILD_DIR"
    ninja install
    cd ..
    
    print_success "Installation completed in $INSTALL_DIR/"
}

# Main execution
main() {
    print_status "Starting CANoeLite build process..."
    print_status "Build type: $BUILD_TYPE"
    
    check_msys2
    check_dependencies
    
    # Parse command line arguments
    case "${2:-}" in
        "clean")
            clean_build
            ;;
        "")
            # Default behavior - continue with build
            ;;
        *)
            print_error "Unknown option: $2"
            echo "Usage: $0 [Debug|Release] [clean]"
            exit 1
            ;;
    esac
    
    configure_project
    build_project
    run_tests
    install_project

    print_success "CANoeLite build process completed successfully!"
    print_status "Executable location: $BUILD_DIR/bin/CANoeLite.exe"
    print_status "Installation location: $INSTALL_DIR/"
    
    if [ "$BUILD_TYPE" == "Release" ]; then
        print_status "To run the installed version:"
        print_status "  cd $INSTALL_DIR/bin && ./CANoeLite.exe"
    else
        print_status "To run the debug version:"
        print_status "  cd $BUILD_DIR/bin && ./CANoeLite.exe"
    fi
}

# Show usage if help is requested
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    echo "CANoeLite Build Script"
    echo ""
    echo "Usage: $0 [BUILD_TYPE] [OPTION]"
    echo ""
    echo "BUILD_TYPE:"
    echo "  Debug     Build debug version (default)"
    echo "  Release   Build release version"
    echo ""
    echo "OPTION:"
    echo "  clean     Clean build and install directories before building"
    echo ""
    echo "Examples:"
    echo "  $0                    # Build debug version"
    echo "  $0 Release            # Build release version"
    echo "  $0 Debug clean        # Clean and build debug version"
    echo "  $0 Release clean      # Clean and build release version"
    exit 0
fi

# Run main function
main "$@"

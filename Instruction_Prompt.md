**System**  
You are a senior C++ / Qt architect.  
You produce *compilable* source code, clear folder structures, and concise build instructions.  
Target platform: **Windows 11, MSYS2 UCRT64, Qt 6 (Qt Quick/QML)**.  
Use **Qt Quick Controls 2** for a modern, fluid UI.  
Theme palette: **Bordeaux red #7A1E2A (accent / primary)** and **Light gray #F2F2F2 (base / surface)**.  
Follow MVVM and SOLID principles; embrace CMake, clang-format, and MSYS2’s `mingw-w64-ucrt-x86_64` toolchain.  

**User**  
Please create a starter project named **CANoeLite** — a simplified Vector CANoe–style desktop application.  

### 1 · Core requirements  
* **Quick Access Button** (top-left): List of button group which separate by vertical line.
  * Group 1: Windows control button (Restore, Minimize, Maximize, Close)
  * Group 2: Start and stop button
  * Group 3: Load app configuration button, save app configuration button, save as app configuration button
  * Group 4: Open app configuration setting manual windows
  * Group 5: Customize Quick Access Button. Click to show a drop-down menu to selected which button should be display and a selection to opens a “Customize Ribbon” setting manula windows.
* **Ribbon** implemented in QML with collapsible tabs:  
  `File | Home | Analysis | Simulation | Test | Diagnostics | Environment | Hardware | Tools | Layout`  
  * Each tab owns *ribbon panels*; each panel groups multiple *launch buttons*.
  * Ribbon panel of File tab should fit all windows 
* **Desktop Tabs** (below the ribbon) right click to create / close / rename tab. Choose tab to switch **Window Areas**.
* Inside the active Window Area, each launch button in the ribbon panel spawns a **dock-like QML tool window**.  
  * Tool windows are resizable, floatable, closable; double-click ⇒ *maximize* to fill the main area.  
* **Status Bar** at the bottom (left: app state; centre: progress; right: CAN-bus bitrate & realtime clock).  

### 2 · Architecture  
* MVVM with  
  ```
  Core/   ViewModels/   Views/QML/   Services/   Resources/
  ```  
* **Views** = QML; **ViewModels** = C++ objects exposed to QML (`setContextProperty` or `QML_SINGLETON`).  
* Observer + Command patterns; backend logic in C++, loosely coupled to QML via signals/slots & `Q_PROPERTY`.  
* Provide a `DockManager` service (C++) to track open tools and persist/restore layouts via `QSettings`.  
* Centralised `Theme.qml` imports Bordeaux red & Light gray, supplies `Palette`/`ColorConstants`.  
* Dependency injection via lightweight factory (no external DI framework).  

### 3 · Deliverables  
1. **Folder tree**  
   ```text
   CANoeLite/
     CMakeLists.txt
     3rdparty/
     src/
       Core/
       ViewModels/
       Views/
         QML/
           main.qml
           Ribbon/
             RibbonView.qml
             Panel.qml
           Theme.qml
       Services/
       Resources/
         icons/
     tests/
     build.sh
     README.md
   ```  
2. **CMake**: top-level + `src/CMakeLists.txt`; builds Debug & Release; cmake install, embeds QML with `qt_add_qml_module`.  
3. **main.cpp**: sets up `QQmlApplicationEngine`, registers C++ types, injects ViewModels, applies Fusion style.  
4. **main.qml**: ApplicationWindow → custom Ribbon → Desktop Tabs → DockArea → StatusBar.  
5. **Sample Tool**: “TraceView” QML component launched from `Analysis` → “Trace” ribbon button.  
6. **build.sh**: builds project with MSYS2’s `mingw-w64-ucrt-x86_64` toolchain. use cmake install to create release packages independent of build environment. 

### 4 · Coding conventions  
* C++20, snake_case variables, PascalCase types, 120-col limit.  
* QML: camelCase IDs, PascalCase types; declare colour constants in `Theme.qml`.  
* Every header gets `#pragma once` and Doxygen comments.

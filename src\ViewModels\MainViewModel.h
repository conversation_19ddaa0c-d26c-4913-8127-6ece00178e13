/**
 * @file MainViewModel.h
 * @brief Main view model for CANoeLite application
 * <AUTHOR> Team
 * @date 2025
 */

#pragma once

#include <QObject>
#include <QString>
#include <QStringList>

namespace ViewModels {

/**
 * @brief Main view model that handles application-level operations
 */
class MainViewModel : public QObject
{
    Q_OBJECT
    Q_PROPERTY(bool isSimulationRunning READ isSimulationRunning NOTIFY simulationStateChanged)
    Q_PROPERTY(QString currentConfiguration READ currentConfiguration NOTIFY configurationChanged)
    Q_PROPERTY(QStringList recentConfigurations READ recentConfigurations NOTIFY recentConfigurationsChanged)

public:
    /**
     * @brief Constructor
     * @param parent Parent object
     */
    explicit MainViewModel(QObject* parent = nullptr);
    
    /**
     * @brief Get simulation running state
     * @return True if simulation is running
     */
    bool isSimulationRunning() const { return m_isSimulationRunning; }
    
    /**
     * @brief Get current configuration name
     * @return Current configuration name
     */
    QString currentConfiguration() const { return m_currentConfiguration; }
    
    /**
     * @brief Get list of recent configurations
     * @return List of recent configuration names
     */
    QStringList recentConfigurations() const { return m_recentConfigurations; }

public slots:
    /**
     * @brief Start simulation
     */
    void startSimulation();
    
    /**
     * @brief Stop simulation
     */
    void stopSimulation();
    
    /**
     * @brief Load configuration from file
     */
    void loadConfiguration();
    
    /**
     * @brief Save current configuration
     */
    void saveConfiguration();
    
    /**
     * @brief Save configuration with new name
     */
    void saveAsConfiguration();
    
    /**
     * @brief Open application settings
     */
    void openSettings();
    
    /**
     * @brief Customize quick access toolbar
     */
    void customizeQuickAccess();
    
    /**
     * @brief Customize ribbon interface
     */
    void customizeRibbon();
    
    /**
     * @brief Create new desktop tab
     */
    void createNewTab();
    
    /**
     * @brief Close current desktop tab
     */
    void closeCurrentTab();
    
    /**
     * @brief Rename current desktop tab
     */
    void renameCurrentTab();

signals:
    /**
     * @brief Emitted when simulation state changes
     */
    void simulationStateChanged();
    
    /**
     * @brief Emitted when configuration changes
     */
    void configurationChanged();
    
    /**
     * @brief Emitted when recent configurations list changes
     */
    void recentConfigurationsChanged();

private:
    /**
     * @brief Load recent configurations from settings
     */
    void loadRecentConfigurations();
    
    /**
     * @brief Save recent configurations to settings
     */
    void saveRecentConfigurations();
    
    /**
     * @brief Add configuration to recent list
     * @param configPath Configuration file path
     */
    void addToRecentConfigurations(const QString& configPath);

private:
    bool m_isSimulationRunning;         ///< Simulation running state
    QString m_currentConfiguration;     ///< Current configuration name
    QStringList m_recentConfigurations; ///< Recent configurations list
};

} // namespace ViewModels

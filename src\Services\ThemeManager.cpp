/**
 * @file ThemeManager.cpp
 * @brief Implementation of ThemeManager service
 * <AUTHOR> Team
 * @date 2025
 */

#include "ThemeManager.h"
#include <QSettings>
#include <QDebug>

namespace Services {

ThemeManager::ThemeManager(QObject* parent)
    : QObject(parent)
    , m_currentTheme("Light")
    , m_primaryColor("#7A1E2A")     // Bordeaux red
    , m_surfaceColor("#F2F2F2")     // Light gray
    , m_themeType(ThemeType::Light)
{
    loadThemeSettings();
}

void ThemeManager::setCurrentTheme(const QString& theme)
{
    if (m_currentTheme != theme) {
        m_currentTheme = theme;
        
        // Update theme type based on name
        if (theme.toLower() == "dark") {
            m_themeType = ThemeType::Dark;
        } else if (theme.toLower() == "auto") {
            m_themeType = ThemeType::Auto;
        } else {
            m_themeType = ThemeType::Light;
        }
        
        applyTheme(m_themeType);
        saveThemeSettings();
        emit currentThemeChanged();
    }
}

void ThemeManager::applyTheme(ThemeType themeType)
{
    m_themeType = themeType;
    
    switch (themeType) {
        case ThemeType::Light:
            m_primaryColor = QColor("#7A1E2A");     // Bordeaux red
            m_surfaceColor = QColor("#F2F2F2");     // Light gray
            m_currentTheme = "Light";
            break;
            
        case ThemeType::Dark:
            m_primaryColor = QColor("#A52A3A");     // Lighter bordeaux for dark theme
            m_surfaceColor = QColor("#2D2D2D");     // Dark gray
            m_currentTheme = "Dark";
            break;
            
        case ThemeType::Auto:
            // For now, default to light theme
            // In a full implementation, this would detect system theme
            m_primaryColor = QColor("#7A1E2A");
            m_surfaceColor = QColor("#F2F2F2");
            m_currentTheme = "Auto";
            break;
    }
    
    emit primaryColorChanged();
    emit surfaceColorChanged();
    
    qDebug() << "Applied theme:" << m_currentTheme;
}

void ThemeManager::loadThemeSettings()
{
    QSettings settings;
    
    QString savedTheme = settings.value("theme/current", "Light").toString();
    setCurrentTheme(savedTheme);
    
    qDebug() << "Loaded theme settings:" << savedTheme;
}

void ThemeManager::saveThemeSettings()
{
    QSettings settings;
    
    settings.setValue("theme/current", m_currentTheme);
    settings.sync();
    
    qDebug() << "Saved theme settings:" << m_currentTheme;
}

} // namespace Services

/**
 * @file DockViewModel.h
 * @brief View model for dock window management
 * <AUTHOR> Team
 * @date 2025
 */

#pragma once

#include <QObject>
#include <QStringList>
#include <QVariantMap>

namespace Services {
class DockManager;
}

namespace ViewModels {

/**
 * @brief View model that exposes dock management functionality to QML
 */
class DockViewModel : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QStringList openTools READ openTools NOTIFY openToolsChanged)

public:
    /**
     * @brief Constructor
     * @param dockManager Dock manager service
     * @param parent Parent object
     */
    explicit DockViewModel(Services::DockManager* dockManager, QObject* parent = nullptr);

    /**
     * @brief Get list of open tool names
     * @return List of open tool names
     */
    QStringList openTools() const;

public slots:
    /**
     * @brief Open a tool window
     * @param toolName Name of the tool to open
     */
    void openTool(const QString& toolName);
    
    /**
     * @brief Close a tool window
     * @param toolName Name of the tool to close
     */
    void closeTool(const QString& toolName);
    
    /**
     * @brief Toggle tool window visibility
     * @param toolName Name of the tool to toggle
     */
    void toggleTool(const QString& toolName);
    
    /**
     * @brief Set tool window geometry
     * @param toolName Tool name
     * @param geometry Geometry map
     */
    void setToolGeometry(const QString& toolName, const QVariantMap& geometry);
    
    /**
     * @brief Set tool window floating state
     * @param toolName Tool name
     * @param floating Floating state
     */
    void setToolFloating(const QString& toolName, bool floating);
    
    /**
     * @brief Set tool window maximized state
     * @param toolName Tool name
     * @param maximized Maximized state
     */
    void setToolMaximized(const QString& toolName, bool maximized);
    
    /**
     * @brief Save current layout
     * @param layoutName Layout name
     */
    void saveLayout(const QString& layoutName = "default");
    
    /**
     * @brief Load layout
     * @param layoutName Layout name
     */
    void loadLayout(const QString& layoutName = "default");
    
    /**
     * @brief Get available layout names
     * @return List of available layout names
     */
    QStringList getAvailableLayouts() const;

signals:
    /**
     * @brief Emitted when the list of open tools changes
     */
    void openToolsChanged();
    
    /**
     * @brief Emitted when a tool should be opened
     * @param toolName Tool name
     * @param toolInfo Tool information
     */
    void toolOpened(const QString& toolName, const QVariantMap& toolInfo);
    
    /**
     * @brief Emitted when a tool should be closed
     * @param toolName Tool name
     */
    void toolClosed(const QString& toolName);

private:
    Services::DockManager* m_dockManager;   ///< Dock manager service
};

} // namespace ViewModels

/**
 * @file MeasurementSetupTool.qml
 * @brief Measurement Setup tool window for CANoeLite
 * <AUTHOR> Team
 * @date 2025
 */

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: measurementSetupTool
    
    // Theme colors
    readonly property color primaryColor: "#7A1E2A"        // Bordeaux red
    readonly property color primaryLight: "#A52A3A"        // Lighter bordeaux
    readonly property color surfaceColor: "#F2F2F2"        // Light gray
    readonly property color surfaceLight: "#FFFFFF"        // White
    readonly property color borderColor: "#CCCCCC"         // Border gray
    readonly property color textPrimary: "#212121"         // Dark text
    readonly property color textSecondary: "#757575"       // Medium text
    readonly property color connectedColor: "#4CAF50"      // Green for connected
    readonly property color disconnectedColor: "#F44336"   // Red for disconnected
    readonly property color blueIndicator: "#2196F3"       // Blue for connection node
    
    // State properties
    property bool isOnlineMode: true
    property bool isConnected: false
    
    color: surfaceLight
    border.color: borderColor
    border.width: 1
    
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 8
        spacing: 0
        
        // Title Bar
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 32
            color: primaryColor
            border.color: borderColor
            border.width: 1
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 4
                
                Text {
                    text: "Measurement Setup"
                    color: "white"
                    font.pixelSize: 12
                    font.weight: Font.Bold
                    Layout.fillWidth: true
                }
                
                // Window controls
                Row {
                    spacing: 2
                    
                    ToolButton {
                        width: 20
                        height: 20
                        text: "🗖"
                        ToolTip.text: "Maximize"
                        background: Rectangle {
                            color: parent.hovered ? primaryLight : "transparent"
                            radius: 2
                        }
                        onClicked: console.log("Maximize measurement setup")
                    }
                    
                    ToolButton {
                        width: 20
                        height: 20
                        text: "🗗"
                        ToolTip.text: "Float"
                        background: Rectangle {
                            color: parent.hovered ? primaryLight : "transparent"
                            radius: 2
                        }
                        onClicked: console.log("Float measurement setup")
                    }
                    
                    ToolButton {
                        width: 20
                        height: 20
                        text: "✕"
                        ToolTip.text: "Close"
                        background: Rectangle {
                            color: parent.hovered ? "#E81123" : "transparent"
                            radius: 2
                        }
                        onClicked: console.log("Close measurement setup")
                    }
                }
            }
        }
        
        // Main Content Area
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: surfaceColor
            border.color: borderColor
            border.width: 1
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 16
                spacing: 20
                
                // Left Side - Control Panel
                Rectangle {
                    Layout.preferredWidth: 200
                    Layout.fillHeight: true
                    color: surfaceLight
                    border.color: borderColor
                    border.width: 1
                    radius: 4
                    
                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 12
                        spacing: 16
                        
                        // Drive Section
                        Column {
                            spacing: 8
                            
                            Text {
                                text: "Drive"
                                font.pixelSize: 12
                                font.weight: Font.Bold
                                color: textPrimary
                            }
                            
                            Rectangle {
                                width: 60
                                height: 40
                                color: surfaceColor
                                border.color: borderColor
                                border.width: 1
                                radius: 4
                                
                                Text {
                                    anchors.centerIn: parent
                                    text: "🚗"
                                    font.pixelSize: 20
                                }
                            }
                        }
                        
                        // Mode Toggle Section
                        Column {
                            spacing: 8
                            
                            Rectangle {
                                width: 120
                                height: 80
                                color: surfaceColor
                                border.color: borderColor
                                border.width: 2
                                radius: 6
                                
                                ColumnLayout {
                                    anchors.fill: parent
                                    anchors.margins: 8
                                    spacing: 4
                                    
                                    // Offline/Online Toggle
                                    Button {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 30
                                        text: isOnlineMode ? "Online" : "Offline"
                                        
                                        background: Rectangle {
                                            color: isOnlineMode ? connectedColor : disconnectedColor
                                            border.color: borderColor
                                            border.width: 1
                                            radius: 4
                                        }
                                        
                                        contentItem: Text {
                                            text: parent.text
                                            color: "white"
                                            font.pixelSize: 10
                                            font.weight: Font.Bold
                                            horizontalAlignment: Text.AlignHCenter
                                            verticalAlignment: Text.AlignVCenter
                                        }
                                        
                                        onClicked: {
                                            isOnlineMode = !isOnlineMode
                                            console.log("Mode switched to:", isOnlineMode ? "Online" : "Offline")
                                        }
                                    }
                                    
                                    // Mode-specific button
                                    Button {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 30
                                        text: isOnlineMode ? "Real" : "Drive"
                                        enabled: true
                                        
                                        background: Rectangle {
                                            color: parent.enabled ? (parent.hovered ? primaryLight : surfaceLight) : surfaceColor
                                            border.color: borderColor
                                            border.width: 1
                                            radius: 4
                                        }
                                        
                                        contentItem: Text {
                                            text: parent.text
                                            color: parent.enabled ? textPrimary : textSecondary
                                            font.pixelSize: 10
                                            horizontalAlignment: Text.AlignHCenter
                                            verticalAlignment: Text.AlignVCenter
                                        }
                                        
                                        onClicked: {
                                            if (isOnlineMode) {
                                                canoeOptionsDialog.open()
                                            } else {
                                                offlineModeDialog.open()
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        
                        // Connection State Indicator
                        Column {
                            spacing: 8
                            
                            Text {
                                text: "Connection"
                                font.pixelSize: 10
                                color: textSecondary
                            }
                            
                            Button {
                                width: 80
                                height: 30
                                
                                background: Rectangle {
                                    color: isConnected ? blueIndicator : disconnectedColor
                                    border.color: borderColor
                                    border.width: 1
                                    radius: 4
                                }
                                
                                contentItem: Row {
                                    anchors.centerIn: parent
                                    spacing: 4
                                    
                                    Rectangle {
                                        width: 8
                                        height: 8
                                        radius: 4
                                        color: "white"
                                        anchors.verticalCenter: parent.verticalCenter
                                    }
                                    
                                    Text {
                                        text: isConnected ? "Connected" : "Disconnected"
                                        color: "white"
                                        font.pixelSize: 8
                                        anchors.verticalCenter: parent.verticalCenter
                                    }
                                }
                                
                                onClicked: {
                                    isConnected = !isConnected
                                    console.log("Connection state:", isConnected ? "Connected" : "Disconnected")
                                }
                            }
                        }
                        
                        Item {
                            Layout.fillHeight: true
                        }
                    }
                }
                
                // Right Side - Measurement Tree (Placeholder)
                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: surfaceLight
                    border.color: borderColor
                    border.width: 1
                    radius: 4
                    
                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 12
                        spacing: 8
                        
                        Text {
                            text: "Measurement Configuration"
                            font.pixelSize: 12
                            font.weight: Font.Bold
                            color: textPrimary
                        }
                        
                        ScrollView {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            
                            Column {
                                width: parent.width
                                spacing: 8
                                
                                Repeater {
                                    model: ["CAN Statistics", "Trace", "Trace 2", "Data", "Graphics", "Logging 2"]
                                    
                                    Rectangle {
                                        width: parent.width - 20
                                        height: 40
                                        color: surfaceColor
                                        border.color: borderColor
                                        border.width: 1
                                        radius: 4
                                        
                                        Row {
                                            anchors.left: parent.left
                                            anchors.verticalCenter: parent.verticalCenter
                                            anchors.leftMargin: 8
                                            spacing: 8
                                            
                                            Rectangle {
                                                width: 12
                                                height: 12
                                                color: blueIndicator
                                                radius: 2
                                                anchors.verticalCenter: parent.verticalCenter
                                            }
                                            
                                            Text {
                                                text: modelData
                                                font.pixelSize: 10
                                                color: textPrimary
                                                anchors.verticalCenter: parent.verticalCenter
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // CANoe Options Dialog (for Online mode)
    Dialog {
        id: canoeOptionsDialog
        title: "CANoe Options"
        width: 400
        height: 300
        anchors.centerIn: parent
        modal: true

        background: Rectangle {
            color: surfaceLight
            border.color: borderColor
            border.width: 1
            radius: 4
        }

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 16
            spacing: 16

            Text {
                text: "CANoe Real-time Options"
                font.pixelSize: 14
                font.weight: Font.Bold
                color: textPrimary
            }

            Text {
                text: "Configure real-time measurement parameters and settings."
                font.pixelSize: 10
                color: textSecondary
                wrapMode: Text.WordWrap
                Layout.fillWidth: true
            }

            GroupBox {
                title: "Real-time Settings"
                Layout.fillWidth: true
                Layout.fillHeight: true

                ColumnLayout {
                    anchors.fill: parent
                    spacing: 8

                    CheckBox {
                        text: "Enable real-time processing"
                        checked: true
                    }

                    CheckBox {
                        text: "Auto-start measurement"
                        checked: false
                    }

                    CheckBox {
                        text: "High-precision timestamps"
                        checked: true
                    }

                    Item {
                        Layout.fillHeight: true
                    }
                }
            }

            Row {
                Layout.alignment: Qt.AlignRight
                spacing: 8

                Button {
                    text: "OK"
                    onClicked: canoeOptionsDialog.close()
                }

                Button {
                    text: "Cancel"
                    onClicked: canoeOptionsDialog.close()
                }
            }
        }
    }

    // Offline Mode Dialog (for Offline mode)
    Dialog {
        id: offlineModeDialog
        title: "Offline Mode Configuration"
        width: 400
        height: 300
        anchors.centerIn: parent
        modal: true

        background: Rectangle {
            color: surfaceLight
            border.color: borderColor
            border.width: 1
            radius: 4
        }

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 16
            spacing: 16

            Text {
                text: "Offline Drive Configuration"
                font.pixelSize: 14
                font.weight: Font.Bold
                color: textPrimary
            }

            Text {
                text: "Configure offline measurement and drive simulation settings."
                font.pixelSize: 10
                color: textSecondary
                wrapMode: Text.WordWrap
                Layout.fillWidth: true
            }

            GroupBox {
                title: "Drive Settings"
                Layout.fillWidth: true
                Layout.fillHeight: true

                ColumnLayout {
                    anchors.fill: parent
                    spacing: 8

                    CheckBox {
                        text: "Enable drive simulation"
                        checked: true
                    }

                    CheckBox {
                        text: "Load previous session"
                        checked: false
                    }

                    CheckBox {
                        text: "Replay recorded data"
                        checked: true
                    }

                    Item {
                        Layout.fillHeight: true
                    }
                }
            }

            Row {
                Layout.alignment: Qt.AlignRight
                spacing: 8

                Button {
                    text: "OK"
                    onClicked: offlineModeDialog.close()
                }

                Button {
                    text: "Cancel"
                    onClicked: offlineModeDialog.close()
                }
            }
        }
    }
}

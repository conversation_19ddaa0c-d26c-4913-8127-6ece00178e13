/**
 * @file MainViewModel.cpp
 * @brief Implementation of MainViewModel
 * <AUTHOR> Team
 * @date 2025
 */

#include "MainViewModel.h"
#include <QSettings>
#include <QFileDialog>
#include <QMessageBox>
#include <QDebug>

namespace ViewModels {

MainViewModel::MainViewModel(QObject* parent)
    : QObject(parent)
    , m_isSimulationRunning(false)
    , m_currentConfiguration("Untitled")
{
    loadRecentConfigurations();
}

void MainViewModel::startSimulation()
{
    if (!m_isSimulationRunning) {
        m_isSimulationRunning = true;
        emit simulationStateChanged();
        qDebug() << "Simulation started";
    }
}

void MainViewModel::stopSimulation()
{
    if (m_isSimulationRunning) {
        m_isSimulationRunning = false;
        emit simulationStateChanged();
        qDebug() << "Simulation stopped";
    }
}

void MainViewModel::loadConfiguration()
{
    QString fileName = QFileDialog::getOpenFileName(
        nullptr,
        "Load Configuration",
        "",
        "Configuration Files (*.cfg);;All Files (*)"
    );
    
    if (!fileName.isEmpty()) {
        m_currentConfiguration = QFileInfo(fileName).baseName();
        addToRecentConfigurations(fileName);
        emit configurationChanged();
        qDebug() << "Configuration loaded:" << fileName;
    }
}

void MainViewModel::saveConfiguration()
{
    if (m_currentConfiguration == "Untitled") {
        saveAsConfiguration();
        return;
    }
    
    // In a real implementation, this would save the current configuration
    qDebug() << "Configuration saved:" << m_currentConfiguration;
}

void MainViewModel::saveAsConfiguration()
{
    QString fileName = QFileDialog::getSaveFileName(
        nullptr,
        "Save Configuration As",
        m_currentConfiguration + ".cfg",
        "Configuration Files (*.cfg);;All Files (*)"
    );
    
    if (!fileName.isEmpty()) {
        m_currentConfiguration = QFileInfo(fileName).baseName();
        addToRecentConfigurations(fileName);
        emit configurationChanged();
        qDebug() << "Configuration saved as:" << fileName;
    }
}

void MainViewModel::openSettings()
{
    // In a real implementation, this would open a settings dialog
    qDebug() << "Opening settings dialog";
}

void MainViewModel::customizeQuickAccess()
{
    // In a real implementation, this would open quick access customization dialog
    qDebug() << "Opening quick access customization";
}

void MainViewModel::customizeRibbon()
{
    // In a real implementation, this would open ribbon customization dialog
    qDebug() << "Opening ribbon customization";
}

void MainViewModel::createNewTab()
{
    // In a real implementation, this would create a new desktop tab
    qDebug() << "Creating new desktop tab";
}

void MainViewModel::closeCurrentTab()
{
    // In a real implementation, this would close the current desktop tab
    qDebug() << "Closing current desktop tab";
}

void MainViewModel::renameCurrentTab()
{
    // In a real implementation, this would open a rename dialog for the current tab
    qDebug() << "Renaming current desktop tab";
}

void MainViewModel::loadRecentConfigurations()
{
    QSettings settings;
    m_recentConfigurations = settings.value("recentConfigurations", QStringList()).toStringList();
    
    // Limit to 10 recent items
    if (m_recentConfigurations.size() > 10) {
        m_recentConfigurations = m_recentConfigurations.mid(0, 10);
        saveRecentConfigurations();
    }
}

void MainViewModel::saveRecentConfigurations()
{
    QSettings settings;
    settings.setValue("recentConfigurations", m_recentConfigurations);
    settings.sync();
}

void MainViewModel::addToRecentConfigurations(const QString& configPath)
{
    // Remove if already exists
    m_recentConfigurations.removeAll(configPath);
    
    // Add to front
    m_recentConfigurations.prepend(configPath);
    
    // Limit to 10 items
    if (m_recentConfigurations.size() > 10) {
        m_recentConfigurations.removeLast();
    }
    
    saveRecentConfigurations();
    emit recentConfigurationsChanged();
}

} // namespace ViewModels

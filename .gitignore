# Build directories
build/
build-*/
install/
*.zip

# CMake generated files
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
Makefile
*.cmake

# Qt generated files
*.qmlc
*.jsc
moc_*.cpp
moc_*.h
qrc_*.cpp
ui_*.h

# Compiler generated files
*.o
*.obj
*.exe
*.dll
*.so
*.dylib
*.a
*.lib

# IDE files
.vscode/
.vs/
*.vcxproj*
*.sln
*.user
*.pro.user*

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Log files
*.log

# Temporary files
*~
*.tmp
*.temp
*.swp
*.swo

# MSYS2 specific
*.exe.stackdump

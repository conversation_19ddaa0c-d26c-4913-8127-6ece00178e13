# CANoeLite source CMakeLists.txt

# Collect source files
file(GLOB_RECURSE SOURCES
    "*.cpp"
    "*.h"
)

# Collect QML files
file(GLOB_RECURSE QML_SOURCES
    "Views/QML/*.qml"
    "Views/QML/*.js"
)

# Create the executable
qt_add_executable(CANoeLite
    main.cpp
    ${SOURCES}
)

# Add QML module
qt_add_qml_module(CANoeLite
    URI CANoeLite
    VERSION 1.0
    QML_FILES
        Views/QML/main.qml
        Views/QML/Theme.qml
        Views/QML/Ribbon/RibbonView.qml
        Views/QML/Ribbon/Panel.qml
        Views/QML/Tools/TraceView.qml
        Views/QML/Tools/Statistics.qml
    SOURCES
        Core/Application.cpp
        Core/Application.h
        ViewModels/MainViewModel.cpp
        ViewModels/MainViewModel.h
        ViewModels/RibbonViewModel.cpp
        ViewModels/RibbonViewModel.h
        ViewModels/DockViewModel.cpp
        ViewModels/DockViewModel.h
        Services/DockManager.cpp
        Services/DockManager.h
        Services/ThemeManager.cpp
        Services/ThemeManager.h
)

# Link Qt6 libraries
target_link_libraries(CANoeLite PRIVATE
    Qt6::Core
    Qt6::Quick
    Qt6::QuickControls2
    Qt6::Widgets
)

# Set target properties
set_target_properties(CANoeLite PROPERTIES
    WIN32_EXECUTABLE TRUE
    MACOSX_BUNDLE TRUE
)

# Include directories
target_include_directories(CANoeLite PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/Core
    ${CMAKE_CURRENT_SOURCE_DIR}/ViewModels
    ${CMAKE_CURRENT_SOURCE_DIR}/Services
)

# Compiler-specific options
if(MSVC)
    target_compile_options(CANoeLite PRIVATE /W4)
else()
    target_compile_options(CANoeLite PRIVATE -Wall -Wextra -Wpedantic)
endif()

# Debug/Release specific settings
target_compile_definitions(CANoeLite PRIVATE
    $<$<CONFIG:Debug>:DEBUG>
    $<$<CONFIG:Release>:NDEBUG>
)

# Resource files
qt_add_resources(CANoeLite "resources"
    PREFIX "/"
    FILES
        Resources/icons/app.ico
)

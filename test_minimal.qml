import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

ApplicationWindow {
    id: window
    width: 800
    height: 600
    visible: true
    title: "CANoeLite Test"
    
    // Theme colors
    readonly property color primaryColor: "#7A1E2A"
    readonly property color surfaceColor: "#F2F2F2"
    readonly property color borderColor: "#CCCCCC"
    readonly property color textPrimary: "#212121"
    
    // Simple RibbonButton component
    component RibbonButton: Button {
        property string buttonText: ""
        property string iconText: "📄"
        
        width: 60
        height: 60
        
        background: Rectangle {
            color: parent.pressed ? window.primaryColor : window.surfaceColor
            border.color: window.borderColor
            border.width: 1
            radius: 3
        }
        
        contentItem: Column {
            anchors.centerIn: parent
            spacing: 2
            
            Text {
                anchors.horizontalCenter: parent.horizontalCenter
                text: parent.parent.iconText
                font.pixelSize: 16
                color: window.textPrimary
            }
            
            Text {
                anchors.horizontalCenter: parent.horizontalCenter
                text: parent.parent.buttonText
                font.pixelSize: 8
                color: window.textPrimary
                wrapMode: Text.WordWrap
                width: parent.parent.width - 8
            }
        }
    }
    
    ColumnLayout {
        anchors.fill: parent
        
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 100
            color: window.surfaceColor
            border.color: window.borderColor
            border.width: 1
            
            Row {
                anchors.centerIn: parent
                spacing: 10
                
                RibbonButton {
                    buttonText: "New"
                    iconText: "📄"
                    onClicked: console.log("New clicked")
                }
                
                RibbonButton {
                    buttonText: "Open"
                    iconText: "📁"
                    onClicked: console.log("Open clicked")
                }
                
                RibbonButton {
                    buttonText: "Save"
                    iconText: "💾"
                    onClicked: console.log("Save clicked")
                }
            }
        }
        
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "#F8F8F8"
            
            Text {
                anchors.centerIn: parent
                text: "Test Area"
                font.pixelSize: 16
            }
        }
    }
}
